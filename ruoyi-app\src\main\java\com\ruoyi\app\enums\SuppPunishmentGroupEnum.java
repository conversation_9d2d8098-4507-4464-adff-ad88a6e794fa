package com.ruoyi.app.enums;

public enum SuppPunishmentGroupEnum {
    QUERY("query", "查阅组"),
    MANAGE("manage", "管理组"),
    INPUT("input","填报组"),
    CONFIRM("confirm","确认组");
    final String code;
    final String info;

    SuppPunishmentGroupEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
