package com.ruoyi.app.apprentice.service;

import java.io.IOException;
import java.util.List;
import com.ruoyi.app.apprentice.domain.ApprenticeBasicInfo;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 以师带徒基本信息Service接口
 *
 * <AUTHOR>
 * @date 2023-08-29
 */
public interface IApprenticeBasicInfoService
{
    /**
     * 查询以师带徒基本信息
     *
     * @param id 以师带徒基本信息ID
     * @return 以师带徒基本信息
     */
    public ApprenticeBasicInfo selectApprenticeBasicInfoById(Long id);
    public ApprenticeBasicInfo selectApprenticeBasicInfoByUserName(String userName);

    /**
     * 查询以师带徒基本信息列表
     *
     * @param apprenticeBasicInfo 以师带徒基本信息
     * @return 以师带徒基本信息集合
     */
    public List<ApprenticeBasicInfo> selectApprenticeBasicInfoList(ApprenticeBasicInfo apprenticeBasicInfo);

    /**
     * 新增以师带徒基本信息
     *
     * @param apprenticeBasicInfo 以师带徒基本信息
     * @return 结果
     */
    public int insertApprenticeBasicInfo(ApprenticeBasicInfo apprenticeBasicInfo);

    /**
     * 修改以师带徒基本信息
     *
     * @param apprenticeBasicInfo 以师带徒基本信息
     * @return 结果
     */
    public int updateApprenticeBasicInfo(ApprenticeBasicInfo apprenticeBasicInfo);

    /**
     * 批量删除以师带徒基本信息
     *
     * @param ids 需要删除的以师带徒基本信息ID
     * @return 结果
     */
    public int deleteApprenticeBasicInfoByIds(Long[] ids);

    /**
     * 删除以师带徒基本信息信息
     *
     * @param id 以师带徒基本信息ID
     * @return 结果
     */
    public int deleteApprenticeBasicInfoById(Long id);

    public void importData(List<ApprenticeBasicInfo> importList, boolean updateSupport);

    public void refreshPermissions();

    /**
     * 汇总导出月度跟踪记录
     *
     * @param request HttpServletRequest
     * @param response HttpServletResponse
     * @param apprenticeBasicInfo 查询条件
     * @throws IOException IO异常
     */
    public void exportByQueryCondition(HttpServletRequest request, HttpServletResponse response, ApprenticeBasicInfo apprenticeBasicInfo) throws IOException;
}