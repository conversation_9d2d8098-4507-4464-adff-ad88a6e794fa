package com.ruoyi.app.supp.controller;

import java.util.List;

import com.ruoyi.app.domain.MaterialInfo;
import com.ruoyi.app.supp.domain.ServiceProject;
import com.ruoyi.app.supp.domain.SuppInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.supp.domain.SuppPunishment;
import com.ruoyi.app.supp.service.ISuppPunishmentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 供应商处罚Controller
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@RestController
@RequestMapping("/supp/punishment")
public class SuppPunishmentController extends BaseController
{
    @Autowired
    private ISuppPunishmentService suppPunishmentService;
    @Autowired
    private ISysUserService userService;
    /**
     * 查询供应商处罚列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SuppPunishment suppPunishment)
    {
        startPage();
        List<SuppPunishment> list = suppPunishmentService.selectSuppPunishmentList(suppPunishment);
        return getDataTable(list);
    }

    /**
     * 查询供应商信息列表
     */
    @GetMapping("/suppInfoList")
    public TableDataInfo suppInfoList(SuppInfo suppInfo)
    {
        startPage();
        List<SuppInfo> list = suppPunishmentService.selectSuppInfoList(suppInfo);
        return getDataTable(list);
    }

    /**
     * 查询物料信息列表
     */
    @GetMapping("/materialInfoList")
    public TableDataInfo materialInfoList(MaterialInfo materialInfo)
    {
        startPage();
        List<MaterialInfo> list = suppPunishmentService.selectMaterialInfoList(materialInfo);
        return getDataTable(list);
    }

    /**
     * 查询服务列表
     */
    @GetMapping("/serviceList")
    public TableDataInfo serviceList(ServiceProject serviceProject)
    {
        startPage();
        List<ServiceProject> list = suppPunishmentService.selectServiceList(serviceProject);
        return getDataTable(list);
    }
    /**
     * 查询工程列表
     */
    @GetMapping("/projectList")
    public TableDataInfo projectList(ServiceProject serviceProject)
    {
        startPage();
        List<ServiceProject> list = suppPunishmentService.selectProjectList(serviceProject);
        return getDataTable(list);
    }

    /**
     * 导出供应商处罚列表
     */
    @Log(title = "供应商处罚", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(SuppPunishment suppPunishment)
    {
        List<SuppPunishment> list = suppPunishmentService.selectSuppPunishmentList(suppPunishment);
        ExcelUtil<SuppPunishment> util = new ExcelUtil<SuppPunishment>(SuppPunishment.class);
        return util.exportExcel(list, "供应商处罚信息表");
    }

    /**
     * 获取供应商处罚详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(suppPunishmentService.selectSuppPunishmentById(id));
    }

    /**
     * 新增供应商处罚
     */
    @Log(title = "供应商处罚", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SuppPunishment suppPunishment)
    {
        return toAjax(suppPunishmentService.insertSuppPunishment(suppPunishment));
    }

    /**
     * 修改供应商处罚
     */
    @Log(title = "供应商处罚", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SuppPunishment suppPunishment)
    {
        return toAjax(suppPunishmentService.updateSuppPunishment(suppPunishment));
    }

    /**
     * 删除供应商处罚
     */
    @Log(title = "供应商处罚", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(suppPunishmentService.deleteSuppPunishmentByIds(ids));
    }
    /**
     * 确认
     */
    @Log(title = "供应商处罚", businessType = BusinessType.UPDATE)
    @PutMapping("/confirm/{ids}")
    public AjaxResult confirm(@PathVariable Long[] ids)
    {
        return AjaxResult.success(suppPunishmentService.confirmSuppPunishmentByIds(ids));
    }

    /**
     * 根据用户工号查用户信息
     * @param userName
     * @return
     */
    @GetMapping(value = "/userName")
    public AjaxResult getUserByUserName(String userName)
    {
        return AjaxResult.success(userService.selectUserByUserName(userName));
    }

    /**
     * 根据登录人工号查分组
     * @return
     */
    @GetMapping(value = "/userGroup")
    public AjaxResult getUserGroupByUserName()
    {
        return AjaxResult.success(suppPunishmentService.selectUserGroupByUserName());
    }

    @GetMapping("/depNameList")
    public AjaxResult depNameList()
    {
        return AjaxResult.success(suppPunishmentService.selectDepNameList());
    }
}
