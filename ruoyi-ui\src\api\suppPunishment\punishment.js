import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listPunishment(query) {
  return request({
    url: '/supp/punishment/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getPunishment(id) {
  return request({
    url: '/supp/punishment/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addPunishment(data) {
  return request({
    url: '/supp/punishment',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updatePunishment(data) {
  return request({
    url: '/supp/punishment',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delPunishment(id) {
  return request({
    url: '/supp/punishment/' + id,
    method: 'delete'
  })
}

// 确认供应商处罚记录
export function confirmPunishment(id) {
  return request({
    url: '/supp/punishment/confirm/' + id,
    method: 'put'
  })
}

// 根据用户名获取单位信息
export function getUserCompany(userName) {
  return request({
    url: '/supp/punishment/userName',
    method: 'get',
    params: { userName: userName }
  })
}

// 导出供应商处罚记录
export function exportPunishment(query) {
  return request({
    url: '/supp/punishment/export',
    method: 'get',
    params: query
  })
}

// 查询用户分组权限
export function getUserGroup() {
  return request({
    url: '/supp/punishment/userGroup',
    method: 'get'
  })
}

// 获取申请部门列表
export function getDepNameList() {
  return request({
    url: '/supp/punishment/depNameList',
    method: 'get'
  })
}