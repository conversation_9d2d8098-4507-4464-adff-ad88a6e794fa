import request from '@/utils/request'

// 查询内部损失成本-产品脱合同损失列表
export function listContractDeviation(query) {
  return request({
    url: '/qualityCost/contractDeviation/list',
    method: 'get',
    params: query
  })
}

// 查询内部损失成本-产品脱合同损失详细
export function getContractDeviation(costCenter) {
  return request({
    url: '/qualityCost/contractDeviation/' + costCenter,
    method: 'get'
  })
}

// 新增内部损失成本-产品脱合同损失
export function addContractDeviation(data) {
  return request({
    url: '/qualityCost/contractDeviation',
    method: 'post',
    data: data
  })
}

// 修改内部损失成本-产品脱合同损失
export function updateContractDeviation(data) {
  return request({
    url: '/qualityCost/contractDeviation',
    method: 'put',
    data: data
  })
}

// 删除内部损失成本-产品脱合同损失
export function delContractDeviation(costCenter) {
  return request({
    url: '/qualityCost/contractDeviation/' + costCenter,
    method: 'delete'
  })
}

// 导出内部损失成本-产品脱合同损失
export function exportContractDeviation(query) {
  return request({
    url: '/qualityCost/contractDeviation/export',
    method: 'get',
    params: query
  })
}

// 查询所有内部损失成本-产品脱合同损失数据
export function listAllContractDeviation(query) {
  return request({
    url: '/qualityCost/contractDeviation/listAll',
    method: 'get',
    params: query
  })
}

// 查询所有内部损失成本-产品脱合同损失数据
export function getSum(query) {
  return request({
    url: '/qualityCost/contractDeviation/getSum',
    method: 'get',
    params: query
  })
}

export function getAllSum(query) {
  return request({
    url: '/qualityCost/contractDeviation/getAllSum',
    method: 'get',
    params: query
  })
}


