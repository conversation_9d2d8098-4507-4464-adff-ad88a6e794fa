package com.ruoyi.web.controller.xctg;

import com.ruoyi.app.apprentice.domain.ApprenticeBasicInfo;
import com.ruoyi.app.apprentice.service.IApprenticeBasicInfoService;
import com.ruoyi.app.eventTrack.domain.TEtEventImport;
import com.ruoyi.app.service.ICommonService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 以师带徒基本信息Controller
 *
 * <AUTHOR>
 * @date 2023-08-29
 */
@RestController
@RequestMapping("/web/apprentice/info")
public class WebApprenticeBasicInfoController extends BaseController
{
    @Autowired
    private IApprenticeBasicInfoService apprenticeBasicInfoService;
    @Autowired
    private ICommonService commonService;

    /**
     * 查询以师带徒基本信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ApprenticeBasicInfo apprenticeBasicInfo)
    {
        startPage();
        List<ApprenticeBasicInfo> list = apprenticeBasicInfoService.selectApprenticeBasicInfoList(apprenticeBasicInfo);
        return getDataTable(list);
    }



    /**
     * 导出以师带徒基本信息列表
     */

    @Log(title = "以师带徒基本信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ApprenticeBasicInfo apprenticeBasicInfo)
    {
        List<ApprenticeBasicInfo> list = apprenticeBasicInfoService.selectApprenticeBasicInfoList(apprenticeBasicInfo);
        ExcelUtil<ApprenticeBasicInfo> util = new ExcelUtil<ApprenticeBasicInfo>(ApprenticeBasicInfo.class);
        return util.exportExcel(list, "info");
    }

    /**
     * 获取以师带徒基本信息详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(apprenticeBasicInfoService.selectApprenticeBasicInfoById(id));
    }

    /**
     * 新增以师带徒基本信息
     */

    @Log(title = "以师带徒基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ApprenticeBasicInfo apprenticeBasicInfo)
    {
        ApprenticeBasicInfo info = apprenticeBasicInfoService.selectApprenticeBasicInfoByUserName(apprenticeBasicInfo.getUserName());
        if(info != null){
            return AjaxResult.error("该工号信息已存在");
        }
        if(commonService.getUserNameByWorkNo(apprenticeBasicInfo.getUserName())==null)
        {
            return AjaxResult.error("该工号不存在");
        }
        if((apprenticeBasicInfo.getTeamEvaluateUser()!=null) && (apprenticeBasicInfo.getTeamEvaluateUser()!=""))
        {
            if(commonService.getUserNameByWorkNo(apprenticeBasicInfo.getTeamEvaluateUser())==null)
            {
                return AjaxResult.error("班组评价人工号不存在");
            }
        }
        if((apprenticeBasicInfo.getSupervisorEvaluateUser()!=null) && (apprenticeBasicInfo.getSupervisorEvaluateUser()!=""))
        {
            if(commonService.getUserNameByWorkNo(apprenticeBasicInfo.getSupervisorEvaluateUser())==null)
            {
                return AjaxResult.error("工作导师评价人工号不存在");
            }
        }
        if((apprenticeBasicInfo.getHrEvaluateUser()!=null)&&(apprenticeBasicInfo.getHrEvaluateUser()!=""))
        {
            if(commonService.getUserNameByWorkNo(apprenticeBasicInfo.getHrEvaluateUser())==null)
            {
                return AjaxResult.error("人事评价人工号不存在");
            }
        }
        if((apprenticeBasicInfo.getLeaderEvaluateUser()!=null)&&(apprenticeBasicInfo.getLeaderEvaluateUser()!=""))
        {
            if(commonService.getUserNameByWorkNo(apprenticeBasicInfo.getLeaderEvaluateUser())==null)
            {
                return AjaxResult.error("企业导师评价人工号不存在");
            }
        }

        return toAjax(apprenticeBasicInfoService.insertApprenticeBasicInfo(apprenticeBasicInfo));
    }

    /**
     * 修改以师带徒基本信息
     */

    @Log(title = "以师带徒基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ApprenticeBasicInfo apprenticeBasicInfo)
    {
        if(commonService.getUserNameByWorkNo(apprenticeBasicInfo.getUserName())==null)
        {
            return AjaxResult.error("该工号不存在");
        }
        if(commonService.getUserNameByWorkNo(apprenticeBasicInfo.getUserName())==null)
        {
            return AjaxResult.error("该工号不存在");
        }
        if((apprenticeBasicInfo.getTeamEvaluateUser()!=null) && (apprenticeBasicInfo.getTeamEvaluateUser()!=""))
        {
            if(commonService.getUserNameByWorkNo(apprenticeBasicInfo.getTeamEvaluateUser())==null)
            {
                return AjaxResult.error("班组评价人工号不存在");
            }
        }
        if((apprenticeBasicInfo.getSupervisorEvaluateUser()!=null) && (apprenticeBasicInfo.getSupervisorEvaluateUser()!=""))
        {
            if(commonService.getUserNameByWorkNo(apprenticeBasicInfo.getSupervisorEvaluateUser())==null)
            {
                return AjaxResult.error("工作导师评价人工号不存在");
            }
        }
        if((apprenticeBasicInfo.getHrEvaluateUser()!=null)&&(apprenticeBasicInfo.getHrEvaluateUser()!=""))
        {
            if(commonService.getUserNameByWorkNo(apprenticeBasicInfo.getHrEvaluateUser())==null)
            {
                return AjaxResult.error("人事评价人工号不存在");
            }
        }
        if((apprenticeBasicInfo.getLeaderEvaluateUser()!=null)&&(apprenticeBasicInfo.getLeaderEvaluateUser()!=""))
        {
            if(commonService.getUserNameByWorkNo(apprenticeBasicInfo.getLeaderEvaluateUser())==null)
            {
                return AjaxResult.error("企业导师评价人工号不存在");
            }
        }
        apprenticeBasicInfo.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(apprenticeBasicInfoService.updateApprenticeBasicInfo(apprenticeBasicInfo));
    }

    /**
     * 删除以师带徒基本信息
     */

    @Log(title = "以师带徒基本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(apprenticeBasicInfoService.deleteApprenticeBasicInfoByIds(ids));
    }


    @Log(title = "以师带徒基本信息导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<ApprenticeBasicInfo> util = new ExcelUtil<ApprenticeBasicInfo>(ApprenticeBasicInfo.class);
        List<ApprenticeBasicInfo> importList = util.importExcel(file.getInputStream());
        apprenticeBasicInfoService.importData(importList, updateSupport);
        return util.exportExcel(importList, "导入结果");
    }

    @GetMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<ApprenticeBasicInfo> util = new ExcelUtil<ApprenticeBasicInfo>(ApprenticeBasicInfo.class);
        return util.importTemplateExcel("以师带徒基本信息数据");
    }

    @PutMapping("/refreshPermissions")
    public AjaxResult refreshPermissions()
    {
        apprenticeBasicInfoService.refreshPermissions();
        return AjaxResult.success("权限跟新成功");
    }

    /**
     * 汇总导出月度跟踪记录
     */
    @Log(title = "汇总导出月度跟踪记录", businessType = BusinessType.EXPORT)
    @GetMapping("/exportByEmployYear")
    public void exportByEmployYear(HttpServletRequest request, HttpServletResponse response, ApprenticeBasicInfo apprenticeBasicInfo) throws IOException {
        apprenticeBasicInfoService.exportByQueryCondition(request, response, apprenticeBasicInfo);
    }
}