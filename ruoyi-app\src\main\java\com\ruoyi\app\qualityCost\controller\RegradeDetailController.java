package com.ruoyi.app.qualityCost.controller;

import java.math.BigDecimal;
import java.util.List;

import com.ruoyi.app.qualityCost.domain.ContractDeviation;
import com.ruoyi.app.qualityCost.domain.QualityObjection;
import com.ruoyi.app.qualityCost.domain.ScrapDetail;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.qualityCost.domain.RegradeDetail;
import com.ruoyi.app.qualityCost.service.IRegradeDetailService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 内部损失成本-产品改判损失明细Controller
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@RestController
@RequestMapping("/qualityCost/regradeDetail")
public class RegradeDetailController extends BaseController
{
    @Autowired
    private IRegradeDetailService regradeDetailService;

    /**
     * 查询内部损失成本-产品改判损失明细列表
     */
    @GetMapping("/list")
    public TableDataInfo list(RegradeDetail regradeDetail)
    {
        startPage();
        List<RegradeDetail> list = regradeDetailService.selectRegradeDetailList(regradeDetail);
        return getDataTable(list);
    }

    @GetMapping("/listAll")
    public TableDataInfo listAll(RegradeDetail regradeDetail)
    {


        List<RegradeDetail> list = null;
        if (regradeDetail.getSearchMode().equals("模糊搜索")) {
            startPage();
            list = regradeDetailService.selectRegradeDetailList(regradeDetail);
        } else if (regradeDetail.getSearchMode().equals("精确搜索")) {
            startPage();
            list = regradeDetailService.selectRegradeDetailListByAccurateSearch(regradeDetail);
        }
        return getDataTable(list);
    }

    @GetMapping("/getSum")
    public AjaxResult getSum(RegradeDetail regradeDetail)
    {
        List<RegradeDetail> list = null;
        if (regradeDetail.getSearchMode().equals("模糊搜索")) {
            startPage();
            list = regradeDetailService.selectRegradeDetailList(regradeDetail);
        } else if (regradeDetail.getSearchMode().equals("精确搜索")) {
            startPage();
            list = regradeDetailService.selectRegradeDetailListByAccurateSearch(regradeDetail);
        }
        BigDecimal sumCostTon = BigDecimal.ZERO;
        BigDecimal sumCostPerTon = BigDecimal.ZERO;
        BigDecimal sumCostEx = BigDecimal.ZERO;
        for (RegradeDetail item : list) {
            if (item.getCostTon() != null) {
                sumCostTon = sumCostTon.add(item.getCostTon());
            }
            if (item.getCostPerTon() != null) {
                sumCostPerTon = sumCostPerTon.add(item.getCostPerTon());
            }
            if (item.getCostEx() != null) {
                sumCostEx = sumCostEx.add(item.getCostEx());
            }
        }
        ContractDeviation sumObj = new ContractDeviation();
        sumObj.setCostTon(sumCostTon);
        sumObj.setCostPerTon(sumCostPerTon);
        sumObj.setCostEx(sumCostEx);

        return AjaxResult.success(sumObj);
    }

    @GetMapping("/getAllSum")
    public AjaxResult getAllSum(RegradeDetail regradeDetail)
    {
        List<RegradeDetail> list = null;
        if (regradeDetail.getSearchMode().equals("模糊搜索")) {
            list = regradeDetailService.selectRegradeDetailList(regradeDetail);
        } else if (regradeDetail.getSearchMode().equals("精确搜索")) {
            list = regradeDetailService.selectRegradeDetailListByAccurateSearch(regradeDetail);
        }
        BigDecimal sumCostTon = BigDecimal.ZERO;
        BigDecimal sumCostPerTon = BigDecimal.ZERO;
        BigDecimal sumCostEx = BigDecimal.ZERO;
        for (RegradeDetail item : list) {
            if (item.getCostTon() != null) {
                sumCostTon = sumCostTon.add(item.getCostTon());
            }
            if (item.getCostPerTon() != null) {
                sumCostPerTon = sumCostPerTon.add(item.getCostPerTon());
            }
            if (item.getCostEx() != null) {
                sumCostEx = sumCostEx.add(item.getCostEx());
            }
        }
        RegradeDetail sumObj = new RegradeDetail();
        sumObj.setCostTon(sumCostTon);
        sumObj.setCostPerTon(sumCostPerTon);
        sumObj.setCostEx(sumCostEx);

        return AjaxResult.success(sumObj);
    }

    /**
     * 导出内部损失成本-产品改判损失明细列表
     */
    @Log(title = "内部损失成本-产品改判损失明细", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(RegradeDetail regradeDetail)
    {
        List<RegradeDetail> list = regradeDetailService.selectRegradeDetailList(regradeDetail);
        ExcelUtil<RegradeDetail> util = new ExcelUtil<RegradeDetail>(RegradeDetail.class);
        return util.exportExcel(list, "regradeDetail");
    }

    /**
     * 获取内部损失成本-产品改判损失明细详细信息
     */
    @GetMapping(value = "/{recCreator}")
    public AjaxResult getInfo(@PathVariable("recCreator") String recCreator)
    {
        return AjaxResult.success(regradeDetailService.selectRegradeDetailById(recCreator));
    }

    /**
     * 新增内部损失成本-产品改判损失明细
     */
    @Log(title = "内部损失成本-产品改判损失明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RegradeDetail regradeDetail)
    {
        return toAjax(regradeDetailService.insertRegradeDetail(regradeDetail));
    }

    /**
     * 修改内部损失成本-产品改判损失明细
     */
    @Log(title = "内部损失成本-产品改判损失明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RegradeDetail regradeDetail)
    {
        return toAjax(regradeDetailService.updateRegradeDetail(regradeDetail));
    }

    /**
     * 删除内部损失成本-产品改判损失明细
     */
    @Log(title = "内部损失成本-产品改判损失明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{recCreators}")
    public AjaxResult remove(@PathVariable String[] recCreators)
    {
        return toAjax(regradeDetailService.deleteRegradeDetailByIds(recCreators));
    }
}
