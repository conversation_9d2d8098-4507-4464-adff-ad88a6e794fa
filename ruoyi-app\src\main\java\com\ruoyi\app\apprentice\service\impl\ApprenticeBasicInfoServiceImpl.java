package com.ruoyi.app.apprentice.service.impl;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.app.apprentice.domain.ApprenticeMonthRecord;
import com.ruoyi.app.apprentice.service.IApprenticeMonthRecordService;
import com.ruoyi.app.service.ICommonService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.service.ISysRoleService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.apprentice.mapper.ApprenticeBasicInfoMapper;
import com.ruoyi.app.apprentice.domain.ApprenticeBasicInfo;
import com.ruoyi.app.apprentice.service.IApprenticeBasicInfoService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 以师带徒基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-29
 */
@Service
public class ApprenticeBasicInfoServiceImpl implements IApprenticeBasicInfoService
{
    @Autowired
    private ApprenticeBasicInfoMapper apprenticeBasicInfoMapper;
    @Autowired
    private ICommonService commonService;
    @Autowired
    private IApprenticeMonthRecordService apprenticeMonthRecordService;

    /**
     * 查询以师带徒基本信息
     *
     * @param id 以师带徒基本信息ID
     * @return 以师带徒基本信息
     */
    @Override
    public ApprenticeBasicInfo selectApprenticeBasicInfoById(Long id)
    {
        ApprenticeBasicInfo apprenticeBasicInfo = apprenticeBasicInfoMapper.selectApprenticeBasicInfoById(id);
        if(StringUtils.isNotBlank(apprenticeBasicInfo.getBirthday()))
        {
            int year = LocalDate.now().getYear();
            int birthYear = Integer.valueOf(apprenticeBasicInfo.getBirthday().substring(0,4));
            apprenticeBasicInfo.setAge(year - birthYear + 1);
        }
        return apprenticeBasicInfo;
    }

    @Override
    public ApprenticeBasicInfo selectApprenticeBasicInfoByUserName(String userName) {
        ApprenticeBasicInfo apprenticeBasicInfo = apprenticeBasicInfoMapper.selectApprenticeBasicInfoByUserName(userName);
        if(!Objects.isNull(apprenticeBasicInfo) && StringUtils.isNotBlank(apprenticeBasicInfo.getBirthday()))
        {
            int year = LocalDate.now().getYear();
            int birthYear = Integer.valueOf(apprenticeBasicInfo.getBirthday().substring(0,4));
            apprenticeBasicInfo.setAge(year - birthYear + 1);
        }

        return apprenticeBasicInfo;
    }

    /**
     * 查询以师带徒基本信息列表
     *
     * @param apprenticeBasicInfo 以师带徒基本信息
     * @return 以师带徒基本信息
     */
    @Override
    public List<ApprenticeBasicInfo> selectApprenticeBasicInfoList(ApprenticeBasicInfo apprenticeBasicInfo)
    {
        List<ApprenticeBasicInfo> list = apprenticeBasicInfoMapper.selectApprenticeBasicInfoList(apprenticeBasicInfo);
        list.forEach(x->{
            if(StringUtils.isNotBlank(x.getBirthday()))
            {
                int year = LocalDate.now().getYear();
                int birthYear = Integer.valueOf(x.getBirthday().substring(0,4));
                x.setAge(year - birthYear + 1);
            }

        });
        return list;

    }

    /**
     * 新增以师带徒基本信息
     *
     * @param apprenticeBasicInfo 以师带徒基本信息
     * @return 结果
     */
    @Override
    public int insertApprenticeBasicInfo(ApprenticeBasicInfo apprenticeBasicInfo)
    {
        apprenticeBasicInfo.setCreateTime(DateUtils.getNowDate());
        if(StringUtils.isNotBlank(apprenticeBasicInfo.getBirthday()))
        {
            int year = LocalDate.now().getYear();
            int birthYear = Integer.valueOf(apprenticeBasicInfo.getBirthday().substring(0,4));
            apprenticeBasicInfo.setAge(year - birthYear + 1);
        }
        return apprenticeBasicInfoMapper.insertApprenticeBasicInfo(apprenticeBasicInfo);
    }

    /**
     * 修改以师带徒基本信息
     *
     * @param apprenticeBasicInfo 以师带徒基本信息
     * @return 结果
     */
    @Override
    public int updateApprenticeBasicInfo(ApprenticeBasicInfo apprenticeBasicInfo)
    {
        apprenticeBasicInfo.setUpdateTime(DateUtils.getNowDate());
        if(!Objects.isNull(apprenticeBasicInfo.getBirthday()) && StringUtils.isNotBlank(apprenticeBasicInfo.getBirthday()))
        {
            int year = LocalDate.now().getYear();
            int birthYear = Integer.valueOf(apprenticeBasicInfo.getBirthday().substring(0,4));
            apprenticeBasicInfo.setAge(year - birthYear + 1);
        }
        return apprenticeBasicInfoMapper.updateApprenticeBasicInfo(apprenticeBasicInfo);
    }

    /**
     * 批量删除以师带徒基本信息
     *
     * @param ids 需要删除的以师带徒基本信息ID
     * @return 结果
     */
    @Override
    public int deleteApprenticeBasicInfoByIds(Long[] ids)
    {
        return apprenticeBasicInfoMapper.deleteApprenticeBasicInfoByIds(ids);
    }

    /**
     * 删除以师带徒基本信息信息
     *
     * @param id 以师带徒基本信息ID
     * @return 结果
     */
    @Override
    public int deleteApprenticeBasicInfoById(Long id)
    {
        return apprenticeBasicInfoMapper.deleteApprenticeBasicInfoById(id);
    }

    @Override
    public void importData(List<ApprenticeBasicInfo> importList, boolean updateSupport) {
        for(ApprenticeBasicInfo item : importList){
            if(StringUtils.isBlank(item.getUserName())
                    ||StringUtils.isBlank(item.getName())
                    ||StringUtils.isBlank(item.getBirthday())){
                item.setResult("失败");
                item.setReason("缺少参数：工号、姓名、生日");
                continue;
            }
            if(commonService.getUserNameByWorkNo(item.getUserName().trim())==null)
            {
                item.setResult("失败");
                item.setReason("该工号不存在");
                continue;
            }
            if(item.getTeamEvaluateUser()!=null&&item.getTeamEvaluateUser()!="")
            {
                if(commonService.getUserNameByWorkNo(item.getTeamEvaluateUser().trim())==null)
                {
                    item.setResult("失败");
                    item.setReason("班组评价人工号不存在");
                    continue;
                }
            }
            if(item.getSupervisorEvaluateUser()!=null&&item.getSupervisorEvaluateUser()!="")
            {
                if(commonService.getUserNameByWorkNo(item.getSupervisorEvaluateUser().trim())==null)
                {
                    item.setResult("失败");
                    item.setReason("工作导师评价人工号不存在");
                    continue;
                }
            }
            if(item.getHrEvaluateUser()!=null&&item.getHrEvaluateUser()!="")
            {
                if(commonService.getUserNameByWorkNo(item.getHrEvaluateUser().trim())==null)
                {
                    item.setResult("失败");
                    item.setReason("人事评价人工号不存在");
                    continue;
                }
            }
            if(item.getLeaderEvaluateUser()!=null&&item.getLeaderEvaluateUser()!="")
            {
                if(commonService.getUserNameByWorkNo(item.getLeaderEvaluateUser().trim())==null)
                {
                    item.setResult("失败");
                    item.setReason("企业导师评价人工号不存在");
                    continue;
                }
            }
            if(Objects.nonNull(item.getDeadline())&&(!item.getDeadline().equals(3))&&(!item.getDeadline().equals(24))){
                item.setResult("失败");
                item.setReason("以师带徒期限填3或24");
                continue;
            }
            // 安全地处理可能为null的字段
            if (item.getUserName() != null) {
                item.setUserName(item.getUserName().trim());
            }
            if (item.getTeamEvaluateUser() != null) {
                item.setTeamEvaluateUser(item.getTeamEvaluateUser().trim());
            }
            if (item.getSupervisorEvaluateUser() != null) {
                item.setSupervisorEvaluateUser(item.getSupervisorEvaluateUser().trim());
            }
            item.setHrEvaluateUser(item.getHrEvaluateUser());
            item.setLeaderEvaluateUser(item.getLeaderEvaluateUser());
            List<ApprenticeBasicInfo> basicInfoList = selectApprenticeBasicInfoList(item);
            if(Objects.isNull(basicInfoList)||basicInfoList.size()<=0){
                insertApprenticeBasicInfo(item);
                item.setResult("成功");
                item.setReason("插入成功");
                continue;
            }
            else if(basicInfoList.size() ==1 && updateSupport){
                updateApprenticeBasicInfo(item);
                item.setResult("成功");
                item.setReason("更新成功");
                continue;
            }
            else if(basicInfoList.size() > 1){
                item.setResult("失败");
                item.setReason("存在多条该工号用户");
                continue;
            }
        }
    }

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Override
    public void refreshPermissions() {
        ApprenticeBasicInfo param = new ApprenticeBasicInfo();
        param.setStatus("0");
        List<ApprenticeBasicInfo> basicInfoList = apprenticeBasicInfoMapper.selectApprenticeBasicInfoList(param);
        List<String> apprenticeList = basicInfoList.stream().map(x -> x.getUserName()).filter(x->StringUtils.isNotBlank(x)).distinct().collect(Collectors.toList());

        List<Long> apprenticeIdList = sysUserMapper.selectUserIdsByUserNames(apprenticeList.toArray(new String[apprenticeList.size()]));

        List<String> masterList = new ArrayList<String>();
        List<String> teams = basicInfoList.stream().map(x -> x.getTeamEvaluateUser()).collect(Collectors.toList());
        List<String> sups = basicInfoList.stream().map(x -> x.getSupervisorEvaluateUser()).collect(Collectors.toList());
        List<String> hrs = basicInfoList.stream().map(x -> x.getHrEvaluateUser()).collect(Collectors.toList());
        List<String> leaders = basicInfoList.stream().map(x -> x.getLeaderEvaluateUser()).collect(Collectors.toList());
        masterList.addAll(teams);
        masterList.addAll(sups);
        masterList.addAll(hrs);
        masterList.addAll(leaders);
        masterList.stream().filter(x->StringUtils.isNotBlank(x)).distinct().collect(Collectors.toList());

        List<Long> masterIdList = sysUserMapper.selectUserIdsByUserNames(masterList.toArray(new String[masterList.size()]));


        List<Long> LeaderList = sysUserMapper.selectUserIdsByUserNames(leaders.toArray(new String[masterList.size()]));



        SysRole apprenticeSubmit = sysRoleService.selectRoleByRoleKey("apprenticeSubmit");
        SysRole apprenticeEvaluate = sysRoleService.selectRoleByRoleKey("apprenticeEvaluate");
        SysRole apprenticeLeader = sysRoleService.selectRoleByRoleKey("apprenticeLeader");

        sysUserRoleMapper.deleteUserRoleByRoleId(apprenticeSubmit.getRoleId());
        List<SysUserRole> apprentices = new ArrayList<>();
        apprenticeIdList.forEach(item->{
            SysUserRole userRole = new SysUserRole();
            userRole.setRoleId(apprenticeSubmit.getRoleId());
            userRole.setUserId(item);
            apprentices.add(userRole);
        });
        sysUserRoleMapper.batchUserRole(apprentices);


        sysUserRoleMapper.deleteUserRoleByRoleId(apprenticeEvaluate.getRoleId());
        List<SysUserRole> masters = new ArrayList<>();
        masterIdList.forEach(item->{
            SysUserRole userRole = new SysUserRole();
            userRole.setRoleId(apprenticeEvaluate.getRoleId());
            userRole.setUserId(item);
            masters.add(userRole);
        });
        sysUserRoleMapper.batchUserRole(masters);

        sysUserRoleMapper.deleteUserRoleByRoleId(apprenticeLeader.getRoleId());
        List<SysUserRole> apprenticeLeaders = new ArrayList<>();
        LeaderList.forEach(item->{
            SysUserRole userRole = new SysUserRole();
            userRole.setRoleId(apprenticeLeader.getRoleId());
            userRole.setUserId(item);
            apprenticeLeaders.add(userRole);
        });
        sysUserRoleMapper.batchUserRole(apprenticeLeaders);

    }

    @Override
    public void exportByQueryCondition(HttpServletRequest request, HttpServletResponse response, ApprenticeBasicInfo apprenticeBasicInfo) throws IOException {
        // 查询符合条件的所有人员，处理多状态查询
        List<ApprenticeBasicInfo> apprenticeList = new ArrayList<>();

        if (StringUtils.isNotBlank(apprenticeBasicInfo.getStatus()) && apprenticeBasicInfo.getStatus().contains(",")) {
            // 处理多状态查询（如"1,2"）
            String[] statusArray = apprenticeBasicInfo.getStatus().split(",");
            for (String status : statusArray) {
                ApprenticeBasicInfo queryParam = new ApprenticeBasicInfo();
                // 复制查询条件
                queryParam.setUserName(apprenticeBasicInfo.getUserName());
                queryParam.setName(apprenticeBasicInfo.getName());
                queryParam.setEmployYear(apprenticeBasicInfo.getEmployYear());
                queryParam.setStatus(status.trim());

                List<ApprenticeBasicInfo> tempList = apprenticeBasicInfoMapper.selectApprenticeBasicInfoList(queryParam);
                apprenticeList.addAll(tempList);
            }
        } else {
            // 单状态查询或无状态查询
            apprenticeList = apprenticeBasicInfoMapper.selectApprenticeBasicInfoList(apprenticeBasicInfo);
        }

        if (apprenticeList.isEmpty()) {
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":500,\"msg\":\"没有找到符合条件的人员记录\"}");
            return;
        }

        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try (ZipOutputStream zos = new ZipOutputStream(baos)) {
            for (ApprenticeBasicInfo apprentice : apprenticeList) {
                // 查询该人员的所有月度跟踪记录（只查询状态为3的记录）
                ApprenticeMonthRecord monthRecordQuery = new ApprenticeMonthRecord();
                monthRecordQuery.setUserName(apprentice.getUserName());
                monthRecordQuery.setDelFlag("0"); // 数据未删除
                monthRecordQuery.setStatus("3"); // 只查询状态为3的记录
                List<ApprenticeMonthRecord> monthRecords = apprenticeMonthRecordService.selectApprenticeMonthRecordList(monthRecordQuery);

                // 跳过没有记录的人员
                if (monthRecords.isEmpty()) {
                    continue;
                }

                // 按时间正序排序
                monthRecords.sort((a, b) -> {
                    if (a.getYearMonth() == null) return 1;
                    if (b.getYearMonth() == null) return -1;
                    return a.getYearMonth().compareTo(b.getYearMonth());
                });

                // 创建Excel文件
                try (Workbook workbook = new XSSFWorkbook();
                     ByteArrayOutputStream excelBaos = new ByteArrayOutputStream()) {

                    Sheet sheet = workbook.createSheet("月度跟踪记录");

                    // 创建标题行
                    Row headerRow = sheet.createRow(0);
                    String[] headers = {"年月", "姓名", "工号", "年龄", "学历", "作业区/科室", "岗位",
                                      "学习情况", "工作情况", "参与课题项目情况", "需解决的问题",
                                      "班组评价", "班组评价人", "工作导师评价", "工作导师评价人", "工作导师评分",
                                      "人事评价", "人事评价人", "企业导师评价", "企业导师评价人", "状态", "创建时间"};

                    // 设置标题样式
                    CellStyle headerStyle = workbook.createCellStyle();
                    Font headerFont = workbook.createFont();
                    headerFont.setBold(true);
                    headerStyle.setFont(headerFont);
                    headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
                    headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                    for (int i = 0; i < headers.length; i++) {
                        Cell cell = headerRow.createCell(i);
                        cell.setCellValue(headers[i]);
                        cell.setCellStyle(headerStyle);
                    }

                    // 填充数据
                    int rowNum = 1;
                    for (ApprenticeMonthRecord record : monthRecords) {
                        Row row = sheet.createRow(rowNum++);
                        row.createCell(0).setCellValue(record.getYearMonth() != null ? record.getYearMonth() : "");
                        row.createCell(1).setCellValue(record.getName() != null ? record.getName() : "");
                        row.createCell(2).setCellValue(record.getUserName() != null ? record.getUserName() : "");
                        row.createCell(3).setCellValue(record.getAge() != null ? record.getAge().toString() : "");
                        row.createCell(4).setCellValue(record.getEducation() != null ? record.getEducation() : "");
                        row.createCell(5).setCellValue(record.getOffice() != null ? record.getOffice() : "");
                        row.createCell(6).setCellValue(record.getPost() != null ? record.getPost() : "");
                        row.createCell(7).setCellValue(record.getLearningSituation() != null ? record.getLearningSituation() : "");
                        row.createCell(8).setCellValue(record.getWorkingCondition() != null ? record.getWorkingCondition() : "");
                        row.createCell(9).setCellValue(record.getProjectSituation() != null ? record.getProjectSituation() : "");
                        row.createCell(10).setCellValue(record.getProblems() != null ? record.getProblems() : "");
                        row.createCell(11).setCellValue(record.getTeamEvaluate() != null ? record.getTeamEvaluate() : "");
                        row.createCell(12).setCellValue(record.getTeamEvaluateUser() != null ? record.getTeamEvaluateUser() : "");
                        row.createCell(13).setCellValue(record.getSupervisorEvaluate() != null ? record.getSupervisorEvaluate() : "");
                        row.createCell(14).setCellValue(record.getSupervisorEvaluateUser() != null ? record.getSupervisorEvaluateUser() : "");
                        row.createCell(15).setCellValue(record.getSupervisorPoint() != null ? record.getSupervisorPoint() : "");
                        row.createCell(16).setCellValue(record.getHrEvaluate() != null ? record.getHrEvaluate() : "");
                        row.createCell(17).setCellValue(record.getHrEvaluateUser() != null ? record.getHrEvaluateUser() : "");
                        row.createCell(18).setCellValue(record.getLeaderEvaluate() != null ? record.getLeaderEvaluate() : "");
                        row.createCell(19).setCellValue(record.getLeaderEvaluateUser() != null ? record.getLeaderEvaluateUser() : "");
                        row.createCell(20).setCellValue(getStatusText(record.getStatus()));
                        row.createCell(21).setCellValue(record.getCreateTime() != null ? record.getCreateTime().toString() : "");
                    }

                    // 自动调整列宽
                    for (int i = 0; i < headers.length; i++) {
                        sheet.autoSizeColumn(i);
                    }

                    // 将Excel写入内存
                    workbook.write(excelBaos);

                    // 处理文件名中的特殊字符
                    String safeName = (apprentice.getName() != null ? apprentice.getName() : "未知姓名")
                        .replaceAll("[\\\\/:*?\"<>|]", "_");
                    String safeUserName = (apprentice.getUserName() != null ? apprentice.getUserName() : "未知工号")
                        .replaceAll("[\\\\/:*?\"<>|]", "_");

                    String fileName = safeName + "(" + safeUserName + ")_月度跟踪记录.xlsx";

                    // 添加到压缩包
                    ZipEntry zipEntry = new ZipEntry(fileName);
                    zos.putNextEntry(zipEntry);
                    zos.write(excelBaos.toByteArray());
                    zos.closeEntry();
                }
            }
        }

        // 检查压缩包是否有内容
        byte[] zipData = baos.toByteArray();
        if (zipData.length == 0) {
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":500,\"msg\":\"生成的压缩包为空，请检查数据\"}");
            return;
        }

        // 生成文件名
        String zipFileName = "历史人员月度跟踪记录.zip";
        if (StringUtils.isNotBlank(apprenticeBasicInfo.getEmployYear())) {
            zipFileName = apprenticeBasicInfo.getEmployYear() + "年入职人员月度跟踪记录.zip";
        } else if (StringUtils.isNotBlank(apprenticeBasicInfo.getName())) {
            zipFileName = "包含" + apprenticeBasicInfo.getName() + "的人员月度跟踪记录.zip";
        }

        // 设置响应头
        response.setContentType("application/zip");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition",
            "attachment; filename*=UTF-8''" + URLEncoder.encode(zipFileName, StandardCharsets.UTF_8.toString()));
        response.setContentLength(zipData.length);

        // 输出压缩包
        response.getOutputStream().write(zipData);
        response.getOutputStream().flush();
    }

    /**
     * 获取状态文本
     */
    private String getStatusText(String status) {
        if (StringUtils.isBlank(status)) {
            return "未知";
        }
        switch (status) {
            case "0": return "草稿";
            case "1": return "已提交";
            case "2": return "班组已评价";
            case "3": return "主管已评价";
            case "4": return "人事已评价";
            case "5": return "领导已评价";
            default: return "未知";
        }
    }
}