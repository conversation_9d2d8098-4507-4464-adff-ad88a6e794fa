import request from '@/utils/request'

// 查询退货明细列表
export function listTACQM03(query) {
  return request({
    url: '/qualityCost/returnDetail/list',
    method: 'get',
    params: query
  })
}

// 查询退货明细详细
export function getTACQM03(recCreator) {
  return request({
    url: '/qualityCost/returnDetail/' + recCreator,
    method: 'get'
  })
}

// 新增退货明细
export function addTACQM03(data) {
  return request({
    url: '/qualityCost/returnDetail',
    method: 'post',
    data: data
  })
}

// 修改退货明细
export function updateTACQM03(data) {
  return request({
    url: '/qualityCost/returnDetail',
    method: 'put',
    data: data
  })
}

// 删除退货明细
export function delTACQM03(recCreator) {
  return request({
    url: '/qualityCost/returnDetail/' + recCreator,
    method: 'delete'
  })
}

// 导出退货明细
export function exportTACQM03(query) {
  return request({
    url: '/qualityCost/returnDetail/export',
    method: 'get',
    params: query
  })
}

export function getAllSum(query) {
  return request({
    url: '/qualityCost/returnDetail/getAllSum',
    method: 'get',
    params: query
  })
}
