<template>
  <div class="app-container">
    <div class="scrap-detail-container">
      <!-- 表格标题 -->
      <div class="table-title">
        <h2>退货明细</h2>
      </div>

      <!-- 表格头部信息 -->
      <div class="table-header-info">
        <!-- <div class="header-item">
          <span class="label">产品报废损失</span>
        </div> -->
        <div class="header-item">
          <span class="label">成本中心名称：</span>
          <el-select v-model="costCenter" placeholder="请选择成本中心" style="width: 160px;" :loading="costCenterLoading">
            <el-option v-for="item in costCenterOptions" :key="item.key" :label="item.label" :value="item.key">
            </el-option>
          </el-select>
        </div>
        <div class="header-item">
          <span class="label">会计期：</span>
          <el-date-picker v-model="accountingPeriod" type="month" placeholder="2025-06" format="yyyy-MM"
            value-format="yyyy-MM" style="width: 150px;">
          </el-date-picker>
        </div>

        <div class="header-item">
          <span class="label">是否计划内：</span>
          <el-select v-model="planFlag" placeholder="全部" style="width: 120px;">
            <el-option v-for="item in planFlagOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </div>

      <!-- 搜索行 -->
      <div class="search-bar-row">
        <div class="header-item">
          <span class="label">钢种：</span>
          <el-input v-model="searchParams.sgStd" placeholder="请输入钢种" style="width: 150px;" clearable />
        </div>
        <!-- <div class="header-item">
          <span class="label">标准：</span>
          <el-input v-model="searchParams.sgStd" placeholder="请输入标准" style="width: 150px;" clearable />
        </div> -->
        <div class="header-item">
          <span class="label">报废原因：</span>
          <el-input v-model="searchParams.reason" placeholder="请输入报废原因" style="width: 150px;" clearable />
        </div>
        <div class="header-item">
          <span class="label">搜索模式：</span>
          <el-select v-model="searchParams.searchMode" placeholder="请选择搜索模式" style="width: 120px;">
            <el-option v-for="item in searchModeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="header-item">
          <el-button type="primary" @click="handleSearch" size="small">搜索</el-button>
          <el-button @click="handleReset" size="small">重置</el-button>
        </div>
      </div>

      <!-- 主表格 -->
      <div class="main-table">
        <el-table :data="tableData" border style="width: auto;" class="scrap-detail-table" v-loading="tableLoading"
          element-loading-text="加载中...">
          <!-- <el-table-column label="成本中心" align="center" prop="costCenter" />
          <el-table-column label="会计期" align="center" prop="yearMonth" /> -->
          <!-- <el-table-column label="产线名称" align="center" prop="factory" /> -->
          <el-table-column label="钢种" align="center" prop="sgSign" />
          <el-table-column label="标准" align="center" prop="sgStd" />

          <el-table-column label="截面" align="center" prop="crShp" />
          <el-table-column label="厚度" align="center" prop="thick" />
          <el-table-column label="宽度" align="center" prop="width" />
          <el-table-column label="长度" align="center" prop="len" />
          <el-table-column label="损失单价" align="center" prop="costPerTon">
            <template #default="scope">
              <span v-if="scope.row.costPerTon !== null && scope.row.costPerTon !== undefined">
                {{ formatNumber(scope.row.costPerTon, 2) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="吨位" align="center" prop="costTon">
            <template #default="scope">
              <span v-if="scope.row.costTon !== null && scope.row.costTon !== undefined">
                {{ formatNumber(scope.row.costTon, 2) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="损失金额" align="center" prop="costEx">
            <template #default="scope">
              <span v-if="scope.row.costEx !== null && scope.row.costEx !== undefined">
                {{ formatNumber(scope.row.costEx, 2) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="报废原因" align="center" prop="reason" />
          <el-table-column prop="planFlag" label="是否计划内" align="center">
            <template #default="scope">
              <el-tag :type="getPlanFlagTagType(scope.row.planFlag)">
                {{ getPlanFlagValue(scope.row.planFlag) }}
              </el-tag>
            </template>
          </el-table-column>


          <!-- <el-table-column label="截面" align="center" prop="crShp" /> -->
          <!-- <el-table-column label="钢种标准成本" align="center" prop="costPerTon1" />
          <el-table-column label="废钢价值" align="center" prop="costPerTon2" />
          <el-table-column label="合金价值" align="center" prop="costPerTon3" /> -->
        </el-table>
      </div>

      <!-- 小计行 -->
      <div class="subtotal-section">
        <el-table :data="subtotalData" border style="width: auto;" class="subtotal-table" :show-header="false"
          :span-method="subtotalSpanMethod">
          <el-table-column prop="label" label="" align="center" />
          <el-table-column prop="empty1" label="" align="center" />
          <el-table-column prop="empty2" label="" align="center" />
          <el-table-column prop="empty3" label="" align="center" />
          <el-table-column prop="empty4" label="" align="center" />
          <el-table-column prop="empty5" label="" align="center" />
          <el-table-column prop="empty6" label="" align="center" />
          <!-- <el-table-column prop="empty7" label="" align="center" /> -->

          <!-- <el-table-column prop="empty7" label="" align="center" />
          <el-table-column prop="empty8" label="" align="center" /> -->



          <!-- <el-table-column prop="totalUnitPrice" label="" align="right">
            <template #default="scope">
              {{ formatNumber(scope.row.totalUnitPrice) }}
            </template>
          </el-table-column> -->
          <el-table-column prop="totalTonnage" label="" align="right">
            <template #default="scope">
              {{ formatNumber(scope.row.totalTonnage, 2) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalAmount" label="" align="right">
            <template #default="scope">
              {{ formatNumber(scope.row.totalAmount, 2) }}
            </template>
          </el-table-column>

          <!-- <el-table-column prop="empty6" label="" align="center" /> -->
          <el-table-column prop="empty7" label="" align="center" />
          <el-table-column prop="empty5" label="" align="center" />

        </el-table>
      </div>

      <!-- 总计行 -->
      <div class="subtotal-section">
        <el-table :data="totalData" border style="width: auto;" class="subtotal-table" :show-header="false"
          :span-method="subtotalSpanMethod">
          <el-table-column prop="label" label="" align="center" />
          <el-table-column prop="empty1" label="" align="center" />
          <el-table-column prop="empty2" label="" align="center" />
          <el-table-column prop="empty3" label="" align="center" />
          <el-table-column prop="empty4" label="" align="center" />
          <el-table-column prop="empty5" label="" align="center" />
          <el-table-column prop="empty6" label="" align="center" />
          <el-table-column prop="totalTonnage" label="" align="right">
            <template #default="scope">
              {{ formatNumber(scope.row.totalTonnage, 2) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalAmount" label="" align="right">
            <template #default="scope">
              {{ formatNumber(scope.row.totalAmount, 2) }}
            </template>
          </el-table-column>
          <el-table-column prop="empty7" label="" align="center" />
          <el-table-column prop="empty8" label="" align="center" />
        </el-table>
      </div>

      <pagination :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="fetchTableData" />
    </div>
  </div>
</template>

<script>
import { costCenterlist } from "@/api/qualityCost/qualityCostDetail";
import { listAllQualityObjection, getSum, getAllSum } from "@/api/qualityCost/qualityObjection";

export default {
  name: "ScrapDetail",
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 成本中心和会计期
      costCenter: '',
      accountingPeriod: this.getDefaultYearMonth(),
      // 新增：是否计划内筛选
      planFlag: '1',
      planFlagOptions: [
        { label: '全部', value: '' },
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ],
      // 表格数据
      tableData: [],
      // 表格加载状态
      tableLoading: false,

      // 成本中心选项
      costCenterOptions: [],
      costCenterLoading: false,
      total: 0,
      sumData: {},
      allSumData: {},
      // 新增：搜索参数
      searchParams: {
        sgSign: '',
        sgStd: '',
        reason: '',
        searchMode: '模糊搜索'
      },
      // 搜索模式选项
      searchModeOptions: [
        { label: '模糊搜索', value: '模糊搜索' },
        { label: '精确搜索', value: '精确搜索' }
      ]
    };
  },
  computed: {
    // 计算小计数据
    subtotalData() {
      if (!this.tableData || this.tableData.length === 0) {
        return [{
          label: "产品报废损失小计",
          totalTonnage: 0,
          totalUnitPrice: 0,
          totalAmount: 0
        }];
      }

      const totalTonnage = this.sumData.costTon;

      const totalUnitPrice = this.sumData.costPerTon;

      const totalAmount = this.sumData.costEx;

      return [{
        label: "产品报废损失小计",
        totalTonnage: totalTonnage,
        // totalUnitPrice: totalUnitPrice,
        totalAmount: totalAmount
      }];
    },
    // 总计数据
    totalData() {
      if (!this.tableData || this.tableData.length === 0) {
        return [{
          label: "产品报废损失总计",
          totalTonnage: 0,
          totalAmount: 0
        }];
      }

      const totalTonnage = this.allSumData.costTon;
      const totalAmount = this.allSumData.costEx;

      return [{
        label: "产品报废损失总计",
        totalTonnage: totalTonnage,
        totalAmount: totalAmount
      }];
    }
  },
  watch: {
    // 监听成本中心变化
    costCenter: {
      handler() {
        this.queryParams.pageNum = 1;
        this.fetchTableData();
      }
    },
    // 监听会计期变化
    accountingPeriod: {
      handler() {
        this.queryParams.pageNum = 1;
        this.fetchTableData();
      }
    },
    // 新增：监听是否计划内变化
    planFlag: {
      handler() {
        this.queryParams.pageNum = 1;
        this.fetchTableData();
      }
    }
  },
  mounted() {
    this.getCostCenterList();
    this.queryParams.planFlag = this.planFlag;
  },
  methods: {
    // 获取计划内标志标签类型
    getPlanFlagTagType(planFlag) {
      if (planFlag === '0' || planFlag === 0) {
        return 'danger'; // 绿色
      } else if (planFlag === '1' || planFlag === 1) {
        return 'success'; // 红色
      }
      return 'warning'; // 黄色（未知状态）
    },
    /** 获取默认会计期 */
     getDefaultYearMonth() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1; // 1-12
      const day = now.getDate();
      const hour = now.getHours();

      // 如果今天是本月25号8点前（含25号7:59），则用上个月
      if (day < 28 || (day === 28 && hour < 1)) {
        // 处理1月时的跨年
        const prevMonth = month === 1 ? 12 : month - 1;
        const prevYear = month === 1 ? year - 1 : year;
        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;
      } else {
        return `${year}-${String(month).padStart(2, '0')}`;
      }
    },

    // 获取成本中心列表
    getCostCenterList() {
      this.costCenterLoading = true;
      costCenterlist().then(response => {
        this.costCenterOptions = response.data || [];
        // 如果有数据，设置默认选中第一个
        if (this.costCenterOptions.length > 0) {
          console.log('获取成本中心列表:', this.costCenterOptions);
          this.costCenter = this.costCenterOptions[0].key;
          // 设置默认值后，主动触发一次数据获取
          this.$nextTick(() => {
            this.fetchTableData();
          });
        }
      }).catch(error => {
        console.error('获取成本中心列表失败:', error);
        this.$message.error('获取成本中心列表失败');
      }).finally(() => {
        this.costCenterLoading = false;
      });
    },
    // 获取表格数据
    fetchTableData() {
      // 只有当成本中心和会计期都有值时才请求
      if (!this.costCenter || !this.accountingPeriod) {
        this.tableData = [];
        return;
      }

      this.tableLoading = true;

      // 当选择"江阴兴澄特种钢铁"时，查询所有数据（不传costCenter参数）
      let costCenterParam = this.costCenter;
      const selectedOption = this.costCenterOptions.find(item => item.key === this.costCenter);
      if (selectedOption && selectedOption.label === '公司') {
        costCenterParam = ''; // 设置为空字符串，查询所有数据
      }

      this.queryParams.costCenter = costCenterParam;
      this.queryParams.yearMonth = this.accountingPeriod.replace('-', '');
      this.queryParams.planFlag = this.planFlag;
      this.queryParams.sgSign = this.searchParams.sgSign;
      this.queryParams.sgStd = this.searchParams.sgStd;
      this.queryParams.reason = this.searchParams.reason;
      this.queryParams.searchMode = this.searchParams.searchMode;

      listAllQualityObjection(this.queryParams).then(response => {
        //this.tableData = (response.rows || []).filter(item => item.costEx !== null && item.costEx !== undefined && item.costEx !== 0);
        this.tableData = response.rows || [];
        console.log('获取报废损失数据:', this.tableData);
        this.total = response.total || 0;
      }).catch(error => {
        console.error('获取报废损失数据失败:', error);
        this.$message.error('获取报废损失数据失败');
        this.tableData = [];
        this.total = 0;
      }).finally(() => {
        this.tableLoading = false;
      });

      getSum(this.queryParams).then(response => {
        this.sumData = response.data || [];
      }).catch(error => {
        this.$message.error('获取数据失败');
        this.sumData = [];
      }).finally(() => {
        this.tableLoading = false;
      });

      getAllSum(this.queryParams).then(response => {
        this.allSumData = response.data || [];
      }).catch(error => {
        this.$message.error('获取数据失败');
        this.allSumData = [];
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    // 处理计划内标志值
    getPlanFlagValue(planFlag) {
      if (planFlag === '0' || planFlag === 0) {
        return '否';
      } else if (planFlag === '1' || planFlag === 1) {
        return '是';
      }
      return '未知'; // 既不是0也不是1时显示未知
    },
    // 获取计划内标志标签类型
    getPlanFlagTagType(planFlag) {
      if (planFlag === '0' || planFlag === 0) {
        return 'danger'; // 绿色
      } else if (planFlag === '1' || planFlag === 1) {
        return 'success'; // 红色
      }
      return 'warning'; // 黄色（未知状态）
    },
    // 获取当前年月
    getCurrentMonth() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      return `${year}-${month}`;
    },
    // 格式化数字显示
    formatNumber(value, decimals = 2) {
      if (value === null || value === undefined) return '';
      return Number(value).toLocaleString('zh-CN', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
      });
    },
    subtotalSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 合并前6列为小计标签
      if (columnIndex >= 0 && columnIndex <= 6) {
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: 7
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
      // 其他列保持不变
      return {
        rowspan: 1,
        colspan: 1
      };
    },
    // 搜索按钮点击
    handleSearch() {
      this.queryParams.pageNum = 1;
      this.fetchTableData();
    },
    // 重置按钮点击
    handleReset() {
      this.searchParams = {
        sgSign: '',
        sgStd: '',
        reason: '',
        searchMode: '模糊搜索'
      };
      this.queryParams.pageNum = 1;
      this.fetchTableData();
    }
  }
};
</script>

<style scoped>
.scrap-detail-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
}

.table-title {
  text-align: center;
  margin-bottom: 20px;
}

.table-title h2 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.table-header-info {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 0;
  gap: 24px;
}

.header-item {
  display: flex;
  align-items: center;
}

.header-item .label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.header-item .value {
  color: #303133;
}

.header-item:first-child .label {
  color: #303133;
  font-size: 16px;
}

.search-bar-row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 0;
  gap: 24px;
}

.main-table {
  margin-bottom: 0;
  display: flex;
  justify-content: center;
}

.subtotal-section {
  margin-top: -1px;
  display: flex;
  justify-content: center;
}

/* 表格样式定制 */
.scrap-detail-table {
  font-size: 14px;
}

.scrap-detail-table :deep(.el-table__header-wrapper) {
  background-color: #f5f7fa;
}

.scrap-detail-table :deep(.el-table__header th) {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: bold;
  padding: 12px 0;
}

.scrap-detail-table :deep(.el-table__body tr:nth-child(odd)) {
  background-color: #fafafa;
}

.scrap-detail-table :deep(.el-table__body tr:hover) {
  background-color: #f0f9ff;
}

/* 小计表格样式 */
.subtotal-table {
  font-size: 14px;
}

.subtotal-table :deep(.el-table__header) {
  display: none !important;
}

.subtotal-table :deep(.el-table__header-wrapper) {
  display: none !important;
}

.subtotal-table :deep(.el-table__body tr) {
  background-color: #f0f9ff;
  font-weight: bold;
}

.subtotal-table :deep(.el-table__body td) {
  background-color: #f0f9ff !important;
  padding: 12px 0;
}

/* 标签样式 */
.el-tag {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .table-header-info {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .search-bar-row {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .scrap-detail-table {
    font-size: 12px;
  }

  .scrap-detail-table :deep(.el-table__body td) {
    padding: 8px 0;
  }
}

@media (max-width: 768px) {
  .scrap-detail-container {
    padding: 10px;
  }

  .main-table {
    overflow-x: auto;
  }
}

/* 搜索区域样式 */
.header-item .el-input {
  margin-right: 8px;
}

.header-item .el-button {
  margin-right: 8px;
}

.header-item .el-button:last-child {
  margin-right: 0;
}
</style>
