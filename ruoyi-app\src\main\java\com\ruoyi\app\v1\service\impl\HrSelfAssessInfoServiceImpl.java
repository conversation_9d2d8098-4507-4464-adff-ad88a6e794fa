package com.ruoyi.app.v1.service.impl;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.app.jw.domain.TJwEmployeeExport;
import com.ruoyi.app.v1.domain.*;
import com.ruoyi.app.v1.mapper.*;
import com.ruoyi.app.v1.service.IHrSelfAssessUserService;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ruoyi.app.v1.service.IHrSelfAssessInfoService;
import com.ruoyi.common.core.service.BaseSoServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import java.util.stream.Collectors;

/**
 * 绩效考核-自评信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-26
 */
@Service
public class HrSelfAssessInfoServiceImpl extends BaseSoServiceImpl<HrSelfAssessInfoMapper, HrSelfAssessInfo> implements IHrSelfAssessInfoService
{
    private static final Logger log = LoggerFactory.getLogger(HrSelfAssessInfoServiceImpl.class);
    @Autowired
    private HrSelfAssessInfoMapper hrSelfAssessInfoMapper;
    @Autowired
    private HrSelfAssessUserMapper hrSelfAssessUserMapper;
    @Autowired
    private HrLateralAssessDeptMapper hrLateralAssessDeptMapper;
    @Autowired
    private HrLateralAssessMapper hrLateralAssessMapper;
    @Autowired
    private HrSelfAssessCheckRecordMapper hrSelfAssessCheckRecordMapper;
    @Autowired
    private IHrSelfAssessUserService hrSelfAssessUserService;

    /**
     * 获取单条数据
     * @param id 绩效考核-自评信息id
     * @return 绩效考核-自评信息
     */
    @Override
    public HrSelfAssessInfo get(String id) {
        HrSelfAssessInfo dto = super.get(id);
        return handleLeaderCheckData(dto);
    }

    @Override
    public HrSelfAssessInfo getByUserInfo(HrSelfAssessInfo hrSelfAssessInfo) {
        HrSelfAssessInfo dto = hrSelfAssessInfoMapper.getByUserInfo(hrSelfAssessInfo);
        return handleLeaderCheckData(dto);
    }

    /**
     * 查询绩效考核-自评信息列表
     * @param hrSelfAssessInfo 绩效考核-自评信息
     * @return 绩效考核-自评信息
     */
    @Override
    public List<HrSelfAssessInfo> findList(HrSelfAssessInfo hrSelfAssessInfo) {
        List<HrSelfAssessInfo> hrSelfAssessInfoList = super.findList(hrSelfAssessInfo);
        for(HrSelfAssessInfo info : hrSelfAssessInfoList){
            handleLeaderCheckData(info);
        }
        return hrSelfAssessInfoList;
    }

    @Override
    public List<HrSelfAssessInfo> listToCheck(HrSelfAssessInfo hrSelfAssessInfo) {
        return hrSelfAssessInfoMapper.listToCheck(hrSelfAssessInfo);
    }

    @Override
    public List<HrSelfAssessInfo> listLeaderToCheck(HrSelfAssessInfo hrSelfAssessInfo) {
        String workNo = hrSelfAssessInfo.getLeaderNo();
        String[] userIds = hrSelfAssessUserMapper.selectHrSelfAssessUsersByLeaderWorkNo(workNo);
        if(userIds.length == 0) return new ArrayList<>();
        hrSelfAssessInfo.setUserIds(userIds);
        return hrSelfAssessInfoMapper.listLeaderToCheck(hrSelfAssessInfo);
    }

    @Override
    public List<HrSelfAssessCheckRecord> listChecked(HrSelfAssessCheckRecord hrSelfAssessCheckRecord) {
        return hrSelfAssessCheckRecordMapper.findList(hrSelfAssessCheckRecord);
    }

    @Override
    public List<HrLateralAssess> listBeAssessed(HrSelfAssessInfo hrSelfAssessInfo) {
        HrLateralAssess params = new HrLateralAssess();
        params.setAssessDate(hrSelfAssessInfo.getAssessDate());
        params.setBeAssessedDept(hrSelfAssessInfo.getDeptId());
        return hrLateralAssessMapper.selectBeAssessedInfo(params);
    }

    /**
     * 插入绩效考核-自评信息
     * @param hrSelfAssessInfo 绩效考核-自评信息
     * @return 绩效考核-自评信息
     */
    @Override
    public boolean insert(HrSelfAssessInfo hrSelfAssessInfo,HrSelfAssessUser user) {
        return super.insert(hrSelfAssessInfo);
    }

    @Override
    @Transactional(readOnly = false)
    public boolean save(HrSelfAssessInfo hrSelfAssessInfo) {
        hrSelfAssessInfo.setStatus("0");
        if(StringUtils.isNotNull(hrSelfAssessInfo.getId())){
            return super.update(hrSelfAssessInfo);
        }else {
            return super.insert(hrSelfAssessInfo);
        }
    }

    @Override
    @Transactional(readOnly = false)
    public boolean submit(HrSelfAssessInfo hrSelfAssessInfo, HrSelfAssessUser user) {
        // 判断是否有事业部
        Long[] deptIds = user.getDeptIds();
        // 默认不需要事业部审核
        hrSelfAssessInfo.setType("0");
        HrLateralAssessDept dept = hrLateralAssessDeptMapper.selectHrLateralAssessDeptByDeptId(deptIds[0]);
        HrLateralAssessDept parent = hrLateralAssessDeptMapper.selectHrLateralAssessDeptByDeptId(dept.getParentId()); // 查询父部门
        if(StringUtils.isNotNull(parent)){
            // 获取事业部信息
            HrLateralAssessDept syb = hrLateralAssessDeptMapper.selectHrLateralAssessDeptByDeptName(parent.getDeptName());
            if(StringUtils.isNotNull(syb)){
                hrSelfAssessInfo.setBusinessDeptId(syb.getDeptId());
                hrSelfAssessInfo.setBusinessDeptName(syb.getDeptName());
                hrSelfAssessInfo.setType("1");
            }
        }
        String role = user.getAssessRole();
        // 判断下一步审核
        if(role.equals("0")){
            // 干部
            hrSelfAssessInfo.setStatus("1");  // 一把手审核
        }else if(role.equals("1")){
            // 一把手
            hrSelfAssessInfo.setDeptScore(hrSelfAssessInfo.getSelfScore());  // 部门评分为自评分
            hrSelfAssessInfo.setDeptUserNo(hrSelfAssessInfo.getWorkNo());
            hrSelfAssessInfo.setDeptUserName(hrSelfAssessInfo.getName());
            hrSelfAssessInfo.setStatus("2");  // 事业部审核
            if("0".equals(hrSelfAssessInfo.getType())){
                hrSelfAssessInfo.setStatus("3");  // 运改部/组织部审核
            }else{
                // 判断是否为事业部一把手
                HrSelfAssessUser businessLeaderCheck = new HrSelfAssessUser();
                businessLeaderCheck.setWorkNo(user.getWorkNo());
                businessLeaderCheck.setAssessRole("1"); // 一把手角色
                businessLeaderCheck.setDeptId(hrSelfAssessInfo.getBusinessDeptId()); // 事业部ID
                HrSelfAssessUser businessLeader = hrSelfAssessUserMapper.getAuth(businessLeaderCheck);

                if (StringUtils.isNotNull(businessLeader)) {
                    // 是事业部一把手，跳过事业部审核，直接到运改部/组织部审核
                    hrSelfAssessInfo.setBusinessScore(hrSelfAssessInfo.getDeptScore()); // 事业部评分为部门评分
                    hrSelfAssessInfo.setBusinessUserNo(hrSelfAssessInfo.getWorkNo());
                    hrSelfAssessInfo.setBusinessUserName(hrSelfAssessInfo.getName());
                    hrSelfAssessInfo.setStatus("3"); // 运改部/组织部审核
                } else {
                    // 不是事业部一把手，需要事业部审核
                    hrSelfAssessInfo.setStatus("2"); // 事业部审核
                }
            }
        }
        if(StringUtils.isNotNull(hrSelfAssessInfo.getId())){
//            hrSelfAssessInfoMapper.deleteHrSelfAssessLeaderCheck(hrSelfAssessInfo.getId());
//            batchHrSelfAssessLeaderCheck(user,hrSelfAssessInfo.getId());
            return super.update(hrSelfAssessInfo);
        }else {
            super.insert(hrSelfAssessInfo);
//            hrSelfAssessInfoMapper.deleteHrSelfAssessLeaderCheck(hrSelfAssessInfo.getId());
//            batchHrSelfAssessLeaderCheck(user,hrSelfAssessInfo.getId());
            return true;
        }
    }

    /**
     * 批量插入绩效考核-自评信息
     * @param list
     * @return 绩效考核-自评信息
     */
    @Override
    public boolean batch(List<HrSelfAssessInfo> list) {
        return super.batch(list);
    }

    /**
     * 更新绩效考核-自评信息
     * @param hrSelfAssessInfo 绩效考核-自评信息
     * @return 绩效考核-自评信息
     */
    @Override
    public boolean update(HrSelfAssessInfo hrSelfAssessInfo) {
        return super.update(hrSelfAssessInfo);
    }


    /**
     * 更新绩效考核-审核
     */
    @Override
    @Transactional(readOnly = false)
    public boolean check(HrSelfAssessInfo hrSelfAssessInfo,HrSelfAssessUser hrSelfAssessUser) {
        String id = hrSelfAssessInfo.getId();
        HrSelfAssessInfo currentInfo = hrSelfAssessInfoMapper.get(id);
        if(AuthCheck(hrSelfAssessUser.getWorkNo(),currentInfo)){
            // 判断请求状态是否为最新状态
            if(currentInfo.getStatus().equals(hrSelfAssessInfo.getStatus())){
                hrSelfAssessInfo.setType(currentInfo.getType());
                hrSelfAssessInfo.setUserId(currentInfo.getUserId());
                hrSelfAssessInfo = handleCheck(hrSelfAssessInfo,hrSelfAssessUser);
                // 评分记录
                HrSelfAssessCheckRecord record = new HrSelfAssessCheckRecord();
                record.setInfoId(hrSelfAssessInfo.getId());
                record.setUserId(currentInfo.getUserId());
                record.setWorkNo(currentInfo.getWorkNo());
                record.setAssessDate(currentInfo.getAssessDate());
                record.setDeptId(currentInfo.getDeptId());
                record.setDeptName(currentInfo.getDeptName());
                record.setName(currentInfo.getName());
                record.setJob(currentInfo.getJob());
                record.setPostType(currentInfo.getPostType());
                record.setCreateBy(hrSelfAssessUser.getWorkNo());
                switch (currentInfo.getStatus()) {
                    case "1":
                        record.setType("1");
                        record.setScore(hrSelfAssessInfo.getDeptScore());
                        break;
                    case "2":
                        record.setType("2");
                        record.setScore(hrSelfAssessInfo.getBusinessScore());
                        break;
                    case "3":
                        record.setType("3");
                        record.setScore(hrSelfAssessInfo.getOrganizationScore());
                        break;
                    case "4":
                        record.setType("4");
                        record.setScore(hrSelfAssessInfo.getLeaderScore());

                        break;
                }
                hrSelfAssessCheckRecordMapper.insert(record);
                return super.update(hrSelfAssessInfo);
            }else{
                return false;
            }
        }else {
            return false;
        }
    }

    /**
     * 驳回
     * @param hrSelfAssessInfo
     * @return
     */
    @Override
    @Transactional(readOnly = false)
    public boolean reject(HrSelfAssessInfo hrSelfAssessInfo) {
        // 添加日志，检查参数
        System.out.println("退回操作 - ID: " + hrSelfAssessInfo.getId() + ", 目标状态: " + hrSelfAssessInfo.getStatus());
        int result = hrSelfAssessInfoMapper.reject(hrSelfAssessInfo);
        System.out.println("退回操作结果: " + result);
        return result > 0;
    }

    /**
     * 删除绩效考核-自评信息
     * @param hrSelfAssessInfo 绩效考核-自评信息
     * @return 绩效考核-自评信息
     */
    @Override
    public boolean delete(HrSelfAssessInfo hrSelfAssessInfo) {
        return super.delete(hrSelfAssessInfo);
    }

    /**
     *
     * @param hrSelfAssessInfo 当前处理信息
     * @param hrSelfAssessUser 当前处理用户
     * @return
     */
    public HrSelfAssessInfo handleCheck(HrSelfAssessInfo hrSelfAssessInfo,HrSelfAssessUser hrSelfAssessUser){
        String status = hrSelfAssessInfo.getStatus();
        switch (status) {
            case "1":
                // 一把手审核
                hrSelfAssessInfo.setDeptUserNo(hrSelfAssessUser.getWorkNo());
                hrSelfAssessInfo.setDeptUserName(hrSelfAssessUser.getName());
                if (hrSelfAssessInfo.getType().equals("0")) {
                    // 不需要事业部审核
                    hrSelfAssessInfo.setStatus("3");
                } else {
                    hrSelfAssessInfo.setStatus("2");
                }
                break;
            case "2":
                // 事业部审核
                hrSelfAssessInfo.setStatus("3");
                hrSelfAssessInfo.setBusinessUserNo(hrSelfAssessUser.getWorkNo());
                hrSelfAssessInfo.setBusinessUserName(hrSelfAssessUser.getName());
                break;
            case "3":
                // 运改组织部审核
                hrSelfAssessInfo.setStatus("4");
                hrSelfAssessInfo.setOrganizationUserNo(hrSelfAssessUser.getWorkNo());
                hrSelfAssessInfo.setOrganizationUserName(hrSelfAssessUser.getName());
                break;
            case "4":
                // 总经理审核
                handleLeaderCheck(hrSelfAssessInfo, hrSelfAssessUser);
                break;
        }
        return hrSelfAssessInfo;
    };

    private void handleLeaderCheck(HrSelfAssessInfo hrSelfAssessInfo, HrSelfAssessUser hrSelfAssessUser){
        HrSelfAssessLeaderCheck leaderCheck = new HrSelfAssessLeaderCheck();
        leaderCheck.setWorkNo(hrSelfAssessUser.getWorkNo());
        leaderCheck.setLeaderName(hrSelfAssessUser.getName());
        leaderCheck.setLeaderReview(hrSelfAssessInfo.getLeaderReview());
        leaderCheck.setLeaderScore(hrSelfAssessInfo.getLeaderScore());
        leaderCheck.setInfoId(hrSelfAssessInfo.getId());
        hrSelfAssessInfoMapper.insertHrSelfAssessLeaderCheck(leaderCheck);
        List<HrSelfAssessLeaderCheck> checkList = hrSelfAssessInfoMapper.selectHrSelfAssessLeaderCheckByInfoId(hrSelfAssessInfo.getId());
        String[] leaders = hrSelfAssessUserMapper.selectHrSelfAssessUserLeaderByUserId(hrSelfAssessInfo.getUserId());
        List<String> comList = new ArrayList<>();
        for(HrSelfAssessLeaderCheck check : checkList){
            if(Arrays.stream(leaders).anyMatch(element -> element.equals(check.getWorkNo())) && !comList.contains(check.getWorkNo())){
                comList.add(check.getWorkNo());
            };
        }
        if(leaders.length == comList.size()){
            hrSelfAssessInfo.setStatus("5");
        }
    }

    public boolean AuthCheck(String workNo,HrSelfAssessInfo hrSelfAssessInfo){
        HrSelfAssessUser authParams = new HrSelfAssessUser();
        String status = hrSelfAssessInfo.getStatus();
        authParams.setWorkNo(workNo);
        switch (status) {
            case "1": {
                // 查询用户审批部门角色是否匹配
                authParams.setAssessRole("1");
                authParams.setDeptId(hrSelfAssessInfo.getDeptId());
                HrSelfAssessUser auth = hrSelfAssessUserMapper.getAuth(authParams);
                return StringUtils.isNotNull(auth);
            }
            case "2": {
                // 事业部审核
                authParams.setAssessRole("1");
                authParams.setDeptId(hrSelfAssessInfo.getBusinessDeptId());
                HrSelfAssessUser auth = hrSelfAssessUserMapper.getAuth(authParams);
                if (StringUtils.isNotNull(auth)) {
                    return true;
                } else {
                    authParams.setAssessRole("2");
                    auth = hrSelfAssessUserMapper.getAuth(authParams);
                    return StringUtils.isNotNull(auth);
                }
            }
            case "3":{
                return true;
            }
            case "4": {
                // 条线领导
                authParams.setAssessRole("2");
                authParams.setDeptId(hrSelfAssessInfo.getDeptId());
                HrSelfAssessUser auth = hrSelfAssessUserMapper.getAuth(authParams);
                return StringUtils.isNotNull(auth);
            }
        }
        return false;
    }

    /**
     * 处理总经理评分数据
     * @return
     */
    private HrSelfAssessInfo handleLeaderCheckData(HrSelfAssessInfo hrSelfAssessInfo){
        if(StringUtils.isNull(hrSelfAssessInfo)) return null;
        List<HrSelfAssessLeaderCheck> leaderChecks = hrSelfAssessInfoMapper.selectHrSelfAssessLeaderCheckByInfoId(hrSelfAssessInfo.getId());
        if(leaderChecks.size() > 0){
            BigDecimal leaderScore = new BigDecimal("0");
            BigDecimal count = new BigDecimal("0");
            StringBuilder leaderNames = new StringBuilder();
            for(HrSelfAssessLeaderCheck leaderCheck : leaderChecks) {
                leaderScore = leaderScore.add(leaderCheck.getLeaderScore());
                count = count.add(new BigDecimal("1"));
                leaderNames.append(leaderCheck.getLeaderName());
                leaderNames.append("、");
            }
            leaderNames.deleteCharAt(leaderNames.length() - 1);
            BigDecimal avgScore = leaderScore.divide(count,2, RoundingMode.HALF_UP);
            hrSelfAssessInfo.setLeaderName(leaderNames.toString());
            hrSelfAssessInfo.setLeaderScore(avgScore);
            hrSelfAssessInfo.setLeaderCheckList(leaderChecks);
        }
        return hrSelfAssessInfo;
    }

    // 批量插入关联关系
    private int batchHrSelfAssessLeaderCheck(HrSelfAssessUser hrSelfAssessUser,String infoId){
        // 插入用户总经理评分关联关系
        String[] leaders = hrSelfAssessUser.getLeaders();
        if(!StringUtils.isNotNull(leaders)) return 0;
        List<HrSelfAssessLeaderCheck> leaderList = new ArrayList<>();
        for(String workNo:leaders){
            HrSelfAssessLeaderCheck leader = new HrSelfAssessLeaderCheck();
            leader.setInfoId(infoId);
            leader.setWorkNo(workNo);
            leaderList.add(leader);
        }
        return hrSelfAssessInfoMapper.batchHrSelfAssessLeaderCheck(leaderList);
    }

    @Override
    @Transactional
    public Map<String, Integer> importFinalScore(List<HrSelfAssessInfo> hrSelfAssessInfoList) {
        int count = 0;
        for(HrSelfAssessInfo info : hrSelfAssessInfoList){
            if(StringUtils.isNotNull(info.getWorkNo()) && StringUtils.isNotNull(info.getFinalScore())){
                hrSelfAssessInfoMapper.setFinalScoreByWorkNo(info);
                count += 1;
            }
        }
        Map<String, Integer> result = new HashMap();
        result.put("success",count);
        result.put("fail",hrSelfAssessInfoList.size() - count);
        return result;
    }

    @Override
    public byte[] downloadDetailZip(HrSelfAssessInfo hrSelfAssessInfo) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ZipOutputStream zip = new ZipOutputStream(outputStream);
        generatorDetailZip(hrSelfAssessInfo, zip);
        IOUtils.closeQuietly(zip);
        return outputStream.toByteArray();
    }

    @Override
    public String exportDetail(HrSelfAssessInfo hrSelfAssessInfo) {
        OutputStream out = null;
        XSSFWorkbook wb = null;
        String filename = null;
        String userName = hrSelfAssessInfo.getName();
        try {
            wb = new XSSFWorkbook();
            handleExcel(wb,hrSelfAssessInfo);
            filename = UUID.randomUUID().toString() + "_" + userName + ".xlsx";
            String downloadPath = RuoYiConfig.getDownloadPath() + filename;
            File desc = new File(downloadPath);
            if (!desc.getParentFile().exists())
            {
                desc.getParentFile().mkdirs();
            }
            out = new FileOutputStream(downloadPath);
            wb.write(out);
        }catch (Exception e){
            throw new CustomException("导出Excel失败，请联系管理员！");
        }finally {
            if (wb != null)
            {
                try
                {
                    wb.close();
                }
                catch (IOException e1)
                {
                    log.error("导出月度业绩考核表格失败", e1);
                }
            }
            if (out != null)
            {
                try
                {
                    out.close();
                }
                catch (IOException e1)
                {
                    log.error("导出月度业绩考核表格失败", e1);
                }
            }
        }
        return filename;
    }

    private void handleExcel(XSSFWorkbook wb, HrSelfAssessInfo hrSelfAssessInfo){
        try {
            ObjectMapper mapper = new ObjectMapper();
            List<HrSelfAssessExcel> list = mapper.readValue(hrSelfAssessInfo.getContent(),new TypeReference<List<HrSelfAssessExcel>>(){});
            HrSelfAssessUser user = hrSelfAssessUserMapper.get(hrSelfAssessInfo.getUserId());

//            XSSFWorkbook wb = new SXSSFWorkbook(); //创建工作簿
            XSSFSheet sheet = wb.createSheet();
            // 设置列宽
            sheet.setColumnWidth(0,10*256);
            sheet.setColumnWidth(1,12*256);
            sheet.setColumnWidth(2,36*256);
            sheet.setColumnWidth(3,36*256);
            sheet.setColumnWidth(4,36*256);
            sheet.setColumnWidth(5,8*256);

            // 标题字体
            Font titleFont = wb.createFont();
            titleFont.setFontHeightInPoints((short) 20);
            titleFont.setBold(true); // 加粗

            XSSFRow topRow = sheet.createRow(0);
            topRow.setHeightInPoints(32);
            XSSFCell topCell = topRow.createCell(0);
            CellStyle titleStyle = wb.createCellStyle();
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            titleStyle.setFont(titleFont);
            topCell.setCellStyle(titleStyle);
            if(user.getPostType().equals("0")){
                // 技术类
                topCell.setCellValue("专技人员月度业绩考核表");
            }else{
                // 行政类
                topCell.setCellValue("分厂(部门)行政干部月度业绩报告单");
            }
            if(user.getAssessRole().equals("1")){
                topCell.setCellValue("分厂(部门)及负责人月度业绩报告单");
            }

            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(0,0,0,5));
            sheet.addMergedRegion(new CellRangeAddress(1,1,1,2));
            sheet.addMergedRegion(new CellRangeAddress(1,1,4,5));
            sheet.addMergedRegion(new CellRangeAddress(2,2,0,5));

            CellStyle head1Style = wb.createCellStyle();
            head1Style.setBorderBottom(BorderStyle.THIN);
            head1Style.setBorderTop(BorderStyle.THIN);

            XSSFRow head1 = sheet.createRow(1); //表头(部门、姓名、考核日期)
            head1.setHeightInPoints(16);
            head1.createCell(0).setCellStyle(head1Style);
            XSSFCell deptCell = head1.createCell(1);
            deptCell.setCellValue("部门/分厂：" + hrSelfAssessInfo.getDeptName());
            deptCell.setCellStyle(head1Style);
            head1.createCell(2).setCellStyle(head1Style);
            XSSFCell nameCell = head1.createCell(3);
            nameCell.setCellValue("姓名：" + hrSelfAssessInfo.getName());
            nameCell.setCellStyle(head1Style);
            XSSFCell dateCell = head1.createCell(4);
            dateCell.setCellValue("考核年月：" + new SimpleDateFormat("yyyy年MM月").format(hrSelfAssessInfo.getAssessDate()));
            CellStyle head1Style2 = wb.createCellStyle();
            head1Style2.setBorderBottom(BorderStyle.THIN);
            head1Style2.setBorderTop(BorderStyle.THIN);
            head1Style2.setAlignment(HorizontalAlignment.RIGHT);
            dateCell.setCellStyle(head1Style2);
            head1.createCell(5).setCellStyle(head1Style);

            XSSFRow head2 = sheet.createRow(2); //表头(月度工作业绩报告)
            head2.setHeightInPoints(32);
            XSSFCell titleCell = head2.createCell(0);
            titleCell.setCellValue("月度工作业绩报告");
            CellStyle head2Style = setCellStyle(wb.createCellStyle());
            head2Style.setAlignment(HorizontalAlignment.CENTER);
            Font font = wb.createFont();
            font.setFontHeightInPoints((short) 14);
            font.setBold(true); // 加粗
            head2Style.setFont(font);
            titleCell.setCellStyle(head2Style);

            XSSFRow head3 = sheet.createRow(3); //表头(月度工作业绩报告)
            head3.setHeightInPoints(16);
            String[] headStr = {"类型","指标","目标","评分标准","完成实绩（若扣分，写明原因）","加减分","加减分原因"};
            CellStyle head3Style = setCellStyle(wb.createCellStyle());
            head3Style.setAlignment(HorizontalAlignment.CENTER);
            for(int i = 0; i < headStr.length; i++){
                XSSFCell cell = head3.createCell(i);
                cell.setCellValue(headStr[i]);
                cell.setCellStyle(head3Style);
            }
//            sheet.addMergedRegion(new CellRangeAddress(3,3,1,2));
            CellStyle bodyStyle1 = setCellStyle(wb.createCellStyle());
            CellStyle bodyStyle2 = setCellStyle(wb.createCellStyle());
            bodyStyle2.setAlignment(HorizontalAlignment.CENTER);
            bodyStyle1.setWrapText(true);
            int itemStartFlag = 0;
            int itemEndFlag = 0;
            int standardStartFlag = 0;
            int standardEndFlag = 0;
            List<int[]> itemMergeList = new ArrayList<>();
            List<int[]> standardMergeList = new ArrayList<>();
            for(int i = 0; i < list.size(); i++){
                XSSFRow row = sheet.createRow(i + 4);
//                row.setHeightInPoints(18);
                XSSFCell cell1 = row.createCell(0);
                cell1.setCellValue(list.get(i).getItem());
                cell1.setCellStyle(bodyStyle1);

                XSSFCell cell2 = row.createCell(1);
                cell2.setCellStyle(bodyStyle1);

                XSSFCell cell3 = row.createCell(2);
                cell3.setCellStyle(bodyStyle1);

                if(StringUtils.isNotEmpty(list.get(i).getCategory())){
                    cell2.setCellValue(list.get(i).getCategory());
                    cell3.setCellValue(list.get(i).getTarget());
                }else{
                    cell2.setCellValue(list.get(i).getTarget());
                    sheet.addMergedRegion(new CellRangeAddress(i + 4,i + 4,1,2));
                }


                XSSFCell cell4 = row.createCell(3);
                cell4.setCellValue(list.get(i).getStandard());
                cell4.setCellStyle(bodyStyle1);
                XSSFCell cell5 = row.createCell(4);
                cell5.setCellValue(list.get(i).getPerformance());
                cell5.setCellStyle(bodyStyle1);
                XSSFCell cell6 = row.createCell(5);
                cell6.setCellValue(list.get(i).getDePoints());
                cell6.setCellStyle(bodyStyle2);
                XSSFCell cell7 = row.createCell(6);
                cell7.setCellValue(list.get(i).getPointsReason());
                cell7.setCellStyle(bodyStyle2);



                if(i > 0){
                    if(list.get(i).getItem().equals(list.get(i-1).getItem())){
                        itemEndFlag = i;
                    }else if(itemStartFlag != itemEndFlag){
                        int[] merge = new int[]{itemStartFlag,itemEndFlag};
                        itemMergeList.add(merge);
                        itemStartFlag = i;
                        itemEndFlag = i;
                    }else{
                        itemStartFlag = i;
                        itemEndFlag = i;
                    }
                    if(list.get(i).getStandard().equals(list.get(i-1).getStandard())){
                        standardEndFlag = i;
                    }else if(standardStartFlag != standardEndFlag){
                        int[] merge = new int[]{standardStartFlag,standardEndFlag};
                        standardMergeList.add(merge);
                        standardStartFlag = i;
                        standardEndFlag = i;
                    }else{
                        standardStartFlag = i;
                        standardEndFlag = i;
                    }
                }
            }
            // 单元格合并
            for(int[] merge : itemMergeList){
                sheet.addMergedRegion(new CellRangeAddress(merge[0] + 4,merge[1] + 4,0,0));
            }
            for(int[] merge : standardMergeList){
                sheet.addMergedRegion(new CellRangeAddress(merge[0] + 4,merge[1] + 4,3,3));
            }

            // 签名部分
            XSSFRow scoreTitleRow = sheet.createRow(list.size() + 4);  // 创建标题行
            XSSFRow scoreRow = sheet.createRow(list.size() + 5);  // 创建分数行
            scoreTitleRow.setHeightInPoints(16);
            scoreRow.setHeightInPoints(32);

            XSSFCell selfScoreTitleCell = scoreTitleRow.createCell(0);
            selfScoreTitleCell.setCellValue("自评分/签名");
            selfScoreTitleCell.setCellStyle(head3Style);
            XSSFCell selfScoreCell = scoreRow.createCell(0);
            selfScoreCell.setCellStyle(head3Style);
            selfScoreCell.setCellValue(handleScoreSign(hrSelfAssessInfo.getSelfScore(), hrSelfAssessInfo.getName()));
            scoreTitleRow.createCell(1).setCellStyle(head3Style);
            scoreRow.createCell(1).setCellStyle(head3Style);
            sheet.addMergedRegion(new CellRangeAddress(scoreTitleRow.getRowNum(),scoreTitleRow.getRowNum(),0,1));
            sheet.addMergedRegion(new CellRangeAddress(scoreRow.getRowNum(),scoreRow.getRowNum(),0,1));

            XSSFCell deptScoreTitleCell = scoreTitleRow.createCell(2);
            deptScoreTitleCell.setCellValue("一把手评分/签名");
            deptScoreTitleCell.setCellStyle(head3Style);
            XSSFCell deptScoreCell = scoreRow.createCell(2);
            deptScoreCell.setCellStyle(head3Style);
            deptScoreCell.setCellValue(handleScoreSign(hrSelfAssessInfo.getDeptScore(),hrSelfAssessInfo.getDeptUserName()));

            if(hrSelfAssessInfo.getType().equals("1")){
                // 事业部领导签字
                XSSFCell organizationScoreTitleCell = scoreTitleRow.createCell(3);
                organizationScoreTitleCell.setCellValue("一把手评分/签名");
                organizationScoreTitleCell.setCellStyle(head3Style);
                XSSFCell organizationScoreCell = scoreRow.createCell(3);
                organizationScoreCell.setCellStyle(head3Style);
                organizationScoreCell.setCellValue(handleScoreSign(hrSelfAssessInfo.getOrganizationScore(), hrSelfAssessInfo.getOrganizationUserName()));

                XSSFCell leaderScoreTitleCell = scoreTitleRow.createCell(4);
                leaderScoreTitleCell.setCellValue("总经理部相关领导评分/签名");
                leaderScoreTitleCell.setCellStyle(head3Style);
                XSSFCell leaderScoreCell = scoreRow.createCell(4);
                leaderScoreCell.setCellStyle(head3Style);
                leaderScoreCell.setCellValue(handleScoreSign(hrSelfAssessInfo.getLeaderScore(), hrSelfAssessInfo.getLeaderName()));

                scoreTitleRow.createCell(5).setCellStyle(head3Style);
                scoreRow.createCell(5).setCellStyle(head3Style);

                sheet.addMergedRegion(new CellRangeAddress(scoreTitleRow.getRowNum(),scoreTitleRow.getRowNum(),4,5));
                sheet.addMergedRegion(new CellRangeAddress(scoreRow.getRowNum(),scoreRow.getRowNum(),4,5));
            }else{
                XSSFCell leaderScoreTitleCell = scoreTitleRow.createCell(3);
                leaderScoreTitleCell.setCellValue("总经理部相关领导评分/签名");
                leaderScoreTitleCell.setCellStyle(head3Style);
                XSSFCell leaderScoreCell = scoreRow.createCell(3);
                leaderScoreCell.setCellStyle(head3Style);
                leaderScoreCell.setCellValue(handleScoreSign(hrSelfAssessInfo.getLeaderScore(), hrSelfAssessInfo.getLeaderName()));

                scoreTitleRow.createCell(4).setCellStyle(head3Style);
                scoreRow.createCell(4).setCellStyle(head3Style);
                scoreTitleRow.createCell(5).setCellStyle(head3Style);
                scoreRow.createCell(5).setCellStyle(head3Style);

                sheet.addMergedRegion(new CellRangeAddress(scoreTitleRow.getRowNum(),scoreTitleRow.getRowNum(),3,5));
                sheet.addMergedRegion(new CellRangeAddress(scoreRow.getRowNum(),scoreRow.getRowNum(),3,5));
            }
            
        } catch (IOException e) {
//            e.printStackTrace();
            log.error("导出月度业绩考核表格失败", e);
        }
    }

    // 单元格样式
    private CellStyle setCellStyle(CellStyle style){
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setVerticalAlignment(VerticalAlignment.CENTER);  //垂直居中
        return style;
    }

    // 处理分数签名数据
    private String handleScoreSign(BigDecimal score,String sign){
        StringBuilder str = new StringBuilder();
        if(StringUtils.isNotNull(score)){
            str.append(score);
        }
        if(StringUtils.isNotNull(sign)){
            str.append(" / ");
            str.append(sign);
        }
        return str.toString();
    }

    private void generatorDetailZip(HrSelfAssessInfo hrSelfAssessInfo, ZipOutputStream zip){
        List<HrSelfAssessInfo> hrSelfAssessInfoList = hrSelfAssessInfoMapper.findList(hrSelfAssessInfo);
        for(HrSelfAssessInfo info : hrSelfAssessInfoList){
            try
            {
                handleLeaderCheckData(info);
                XSSFWorkbook wb = new XSSFWorkbook();
                handleExcel(wb,info);
                // 添加到zip
                zip.putNextEntry(new ZipEntry(info.getDeptName() + "/" + info.getName() + ".xlsx"));
                wb.write(zip);
//                IOUtils.write(wb.toString(), zip, Constants.UTF8);
                IOUtils.closeQuietly(wb);
                zip.flush();
                zip.closeEntry();
            }
            catch (IOException e)
            {
                log.error("导出月度业绩考核表格失败", e);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchQuickScore(List<HrSelfAssessInfo> infoList, SysUser currentUser) {
        if (CollectionUtils.isEmpty(infoList)) {
            throw new RuntimeException("评分信息列表不能为空");
        }

        // 获取当前用户信息
//        HrSelfAssessUser currentUser = hrSelfAssessUserService.getByWorkNo(workNo);
//        if (currentUser == null) {
//            throw new RuntimeException("用户信息不存在");
//        }

        // 批量验证权限
        List<String> ids = infoList.stream().map(HrSelfAssessInfo::getId).collect(Collectors.toList());
        List<HrSelfAssessInfo> dbInfoList = hrSelfAssessInfoMapper.selectBatchByIds(ids);
        if (dbInfoList.size() != infoList.size()) {
            throw new RuntimeException("部分待评分信息不存在");
        }

        // 准备更新的评分信息
        List<HrSelfAssessInfo> updateList = new ArrayList<>();
        // 准备新增的评分记录
        List<HrSelfAssessCheckRecord> recordList = new ArrayList<>();
        
        Date now = DateUtils.getNowDate();
        
        for (HrSelfAssessInfo info : infoList) {
            // 获取数据库中的原始信息
            HrSelfAssessInfo dbInfo = dbInfoList.stream()
                .filter(item -> item.getId().equals(info.getId()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("评分信息不存在"));
            
            // 设置更新信息
            HrSelfAssessInfo updateInfo = new HrSelfAssessInfo();
            updateInfo.setId(info.getId());
            updateInfo.setQuickScore(info.getQuickScore());
            updateInfo.setQuickReason(info.getQuickReason());
            updateInfo.setUpdateTime(now);
            updateInfo.setUpdateBy(currentUser.getUserName());

            // 根据状态设置不同的评分字段和评分人
            String status = dbInfo.getStatus();
            switch (status) {
                case "1": // 部门评分
                    updateInfo.setDeptUserNo(currentUser.getUserName());
                    updateInfo.setDeptUserName(currentUser.getNickName());
                    if(dbInfo.getType().equals("1")){
                        updateInfo.setStatus("2");
                    }else {
                        updateInfo.setStatus("3");
                    }
                    break;
                case "2": // 事业部评分
                    updateInfo.setBusinessUserNo(currentUser.getUserName());
                    updateInfo.setBusinessUserName(currentUser.getNickName());
                    updateInfo.setStatus("3");
                    break;
                case "3": // 运改组织部评分
                    updateInfo.setOrganizationUserNo(currentUser.getUserName());
                    updateInfo.setOrganizationUserName(currentUser.getNickName());
                    updateInfo.setStatus("4");
                    break;
                case "4": // 领导评分
                    updateInfo.setLeaderNo(currentUser.getUserName());
                    updateInfo.setLeaderName(currentUser.getNickName());
                    updateInfo.setStatus("5");
                    break;
                default:
                    throw new RuntimeException("评分状态错误，工号：" + dbInfo.getWorkNo());
            }
            updateList.add(updateInfo);
            
            // 设置评分记录
            HrSelfAssessCheckRecord record = new HrSelfAssessCheckRecord();
            record.setInfoId(info.getId());
            record.setAssessDate(dbInfo.getAssessDate());
            record.setWorkNo(dbInfo.getWorkNo());
            record.setName(dbInfo.getName());
            record.setDeptId(dbInfo.getDeptId());
            record.setDeptName(dbInfo.getDeptName());
            record.setJob(dbInfo.getJob());
            // record.setWorkNo(currentUser.getUserName());
            // record.setName(currentUser.getNickName());
            record.setScore(info.getQuickScore());
            record.setCreateTime(now);
            record.setCreateBy(currentUser.getUserName());
            record.setType(status); // 记录评分类型
            record.setPostType(dbInfo.getPostType());
            recordList.add(record);
        }

        // 批量更新评分信息
        int rows = hrSelfAssessInfoMapper.batchUpdateQuickScore(updateList);

        // 批量插入评分记录
        if (!recordList.isEmpty()) {
            hrSelfAssessCheckRecordMapper.batchInsert(recordList);
        }

        return rows;
    }

    @Override
    public List<HrSelfAssessInfo> batchByIds(Long[] ids) {
        return hrSelfAssessInfoMapper.batchByIds(ids);
    }

    @Override
    public List<HrSelfAssessInfo> batchWithBenefitByIds(Long[] ids) {
        try{
            List<HrSelfAssessInfo> hrSelfAssessInfoList = hrSelfAssessInfoMapper.batchByIds(ids);
            for(HrSelfAssessInfo info : hrSelfAssessInfoList){
                ObjectMapper mapper = new ObjectMapper();
                List<HrSelfAssessExcel> list = mapper.readValue(info.getContent(), new TypeReference<List<HrSelfAssessExcel>>() {});
                BigDecimal benefitScore = new BigDecimal("0");
                for(HrSelfAssessExcel item : list){
                    if(item.getCategory().equals("效益")){
                        BigDecimal score = new BigDecimal(item.getDePoints());
                        benefitScore = benefitScore.add(score);
                    }
                }
                info.setBenefitScore(benefitScore);
            }
            return hrSelfAssessInfoList;
        }catch (Exception e){
            log.error("批量获取信息出错:"+e.getMessage());
            throw new RuntimeException("批量获取信息出错");
        }
    }

    @Override
    public String exportTechnicalPerformanceSummary(HrSelfAssessInfo hrSelfAssessInfo) {
        OutputStream out = null;
        XSSFWorkbook wb = null;
        String filename = null;
        
        try {
            // 查询有技术序列干部的条线领导列表
            List<HrSelfAssessUser> leaders = hrSelfAssessInfoMapper.selectLeadersWithTechnicalStaff(hrSelfAssessInfo);
            
            if (leaders.isEmpty()) {
                throw new CustomException("该考核年月没有找到技术序列干部数据！");
            }
            
            wb = new XSSFWorkbook();
            
            // 为每个条线领导创建一个Sheet
            for (HrSelfAssessUser leader : leaders) {
                // 查询该条线领导下的技术序列干部
                HrSelfAssessInfo queryInfo = new HrSelfAssessInfo();
                queryInfo.setAssessDate(hrSelfAssessInfo.getAssessDate());
                queryInfo.setLeaderNo(leader.getWorkNo());
                
                List<HrSelfAssessInfo> technicalStaffList = hrSelfAssessInfoMapper.selectTechnicalStaffByLeader(queryInfo);
                
                if (!technicalStaffList.isEmpty()) {
                    createTechnicalSummarySheet(wb, leader, technicalStaffList, hrSelfAssessInfo.getAssessDate());
                }
            }
            
            // 生成文件名
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年M月");
            String assessDateStr = dateFormat.format(hrSelfAssessInfo.getAssessDate());
            filename = UUID.randomUUID().toString() + "_" + assessDateStr + "技术序列业绩汇总表.xlsx";
            String downloadPath = RuoYiConfig.getDownloadPath() + filename;
            File desc = new File(downloadPath);
            if (!desc.getParentFile().exists()) {
                desc.getParentFile().mkdirs();
            }
            
            out = new FileOutputStream(downloadPath);
            wb.write(out);
            
        } catch (Exception e) {
            log.error("导出技术序列业绩汇总表失败", e);
            throw new CustomException("导出技术序列业绩汇总表失败，请联系管理员！");
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                    log.error("导出技术序列业绩汇总表失败", e1);
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    log.error("导出技术序列业绩汇总表失败", e1);
                }
            }
        }
        
        return filename;
    }
    
    /**
     * 创建技术序列业绩汇总表Sheet
     */
    private void createTechnicalSummarySheet(XSSFWorkbook wb, HrSelfAssessUser leader, 
                                           List<HrSelfAssessInfo> staffList, Date assessDate) {
        
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年M月");
        String assessDateStr = dateFormat.format(assessDate);
        
        // 创建Sheet，名称为条线领导姓名
        XSSFSheet sheet = wb.createSheet(leader.getName());
        
        // 设置列宽
        sheet.setColumnWidth(0, 8 * 256);   // 序号
        sheet.setColumnWidth(1, 16 * 256);  // 部门
        sheet.setColumnWidth(2, 12 * 256);  // 姓名
        sheet.setColumnWidth(3, 16 * 256);  // 职务
        sheet.setColumnWidth(4, 12 * 256);  // 自评分
        sheet.setColumnWidth(5, 16 * 256);  // 部门领导评分
        sheet.setColumnWidth(6, 18 * 256);  // 运改/组织审核得分
        sheet.setColumnWidth(7, 18 * 256);  // 挂钩公司效益得分
        sheet.setColumnWidth(8, 20 * 256);  // 申请加分事项
        sheet.setColumnWidth(9, 12 * 256);  // 加分值
        sheet.setColumnWidth(10, 18 * 256); // 总经理部领导评分
        sheet.setColumnWidth(11, 16 * 256); // 备注
        
        // 创建标题样式
        CellStyle titleStyle = wb.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = wb.createFont();
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setBold(true);
        titleStyle.setFont(titleFont);
        
        // 创建表头样式
        CellStyle headerStyle = setCellStyle(wb.createCellStyle());
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font headerFont = wb.createFont();
        headerFont.setFontHeightInPoints((short) 11);
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        
        // 创建数据样式
        CellStyle dataStyle = setCellStyle(wb.createCellStyle());
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setWrapText(true);
        
        CellStyle numberStyle = setCellStyle(wb.createCellStyle());
        numberStyle.setAlignment(HorizontalAlignment.CENTER);
        numberStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 第一行：标题
        XSSFRow titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(24);
        XSSFCell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(assessDateStr + "技术序列业绩汇总表（" + leader.getName() + "总）");
        titleCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 11));
        
        // 第二行：表头
        XSSFRow headerRow = sheet.createRow(1);
        headerRow.setHeightInPoints(20);
        String[] headers = {
            "序号", "部门", "姓名", "职务", "自评分", "部门领导评分", 
            "运改/组织审核得分", "挂钩公司效益得分", "申请加分事项（封顶加5分）", 
            "加分值", "总经理部领导评分", "备注"
        };
        
        for (int i = 0; i < headers.length; i++) {
            XSSFCell headerCell = headerRow.createCell(i);
            headerCell.setCellValue(headers[i]);
            headerCell.setCellStyle(headerStyle);
        }
        
        // 数据行
        int rowIndex = 2;
        for (int i = 0; i < staffList.size(); i++) {
            HrSelfAssessInfo staff = staffList.get(i);
            XSSFRow dataRow = sheet.createRow(rowIndex++);
            dataRow.setHeightInPoints(18);
            
            // 序号
            XSSFCell seqCell = dataRow.createCell(0);
            seqCell.setCellValue(i + 1);
            seqCell.setCellStyle(numberStyle);
            
            // 部门
            XSSFCell deptCell = dataRow.createCell(1);
            deptCell.setCellValue(staff.getDeptName() != null ? staff.getDeptName() : "");
            deptCell.setCellStyle(dataStyle);
            
            // 姓名
            XSSFCell nameCell = dataRow.createCell(2);
            nameCell.setCellValue(staff.getName() != null ? staff.getName() : "");
            nameCell.setCellStyle(dataStyle);
            
            // 职务
            XSSFCell jobCell = dataRow.createCell(3);
            jobCell.setCellValue(staff.getJob() != null ? staff.getJob() : "");
            jobCell.setCellStyle(dataStyle);
            
            // 自评分
            XSSFCell selfScoreCell = dataRow.createCell(4);
            if (staff.getSelfScore() != null) {
                selfScoreCell.setCellValue(staff.getSelfScore().doubleValue());
            }
            selfScoreCell.setCellStyle(numberStyle);
            
            // 部门领导评分
            XSSFCell deptScoreCell = dataRow.createCell(5);
            if (staff.getDeptScore() != null) {
                deptScoreCell.setCellValue(staff.getDeptScore().doubleValue());
            }
            deptScoreCell.setCellStyle(numberStyle);
            
            // 运改/组织审核得分
            XSSFCell orgScoreCell = dataRow.createCell(6);
            if (staff.getOrganizationScore() != null) {
                orgScoreCell.setCellValue(staff.getOrganizationScore().doubleValue());
            }
            orgScoreCell.setCellStyle(numberStyle);
            
            // 挂钩公司效益得分
            XSSFCell benefitScoreCell = dataRow.createCell(7);
//            if ("Y".equals(staff.getBenefitLinkFlag())) {
//                // 计算效益得分
//                BigDecimal benefitScore = calculateBenefitScore(staff);
//                if (benefitScore != null) {
//                    benefitScoreCell.setCellValue(benefitScore.doubleValue());
//                }
//            } else {
//                benefitScoreCell.setCellValue("不挂钩");
//            }
            if (staff.getOrganizationScore() != null) {
                benefitScoreCell.setCellValue(staff.getOrganizationScore().doubleValue());
            }
            benefitScoreCell.setCellStyle(numberStyle);
            
            // 申请加分事项（从content中解析）
            XSSFCell addPointsItemCell = dataRow.createCell(8);
            String addPointsItems = extractAddPointsItems(staff.getContent());
            addPointsItemCell.setCellValue(addPointsItems);
            addPointsItemCell.setCellStyle(dataStyle);
            
            // 加分值（设置为空字符串）
            XSSFCell addPointsValueCell = dataRow.createCell(9);
            addPointsValueCell.setCellValue("");
            addPointsValueCell.setCellStyle(numberStyle);
            
            // 总经理部领导评分
            XSSFCell leaderScoreCell = dataRow.createCell(10);
            if (staff.getFinalScore() != null) {
                leaderScoreCell.setCellValue(staff.getFinalScore().doubleValue());
            }
            leaderScoreCell.setCellStyle(numberStyle);
            
            // 备注 - 汇总所有加减分理由
            XSSFCell remarkCell = dataRow.createCell(11);
            String remarkContent = extractAllReasons(staff);
            remarkCell.setCellValue(remarkContent);
            remarkCell.setCellStyle(dataStyle);
        }
    }
    
    /**
     * 计算效益得分
     */
    private BigDecimal calculateBenefitScore(HrSelfAssessInfo staff) {
        try {
            if (StringUtils.isEmpty(staff.getContent())) {
                return BigDecimal.ZERO;
            }
            
            ObjectMapper mapper = new ObjectMapper();
            List<HrSelfAssessExcel> list = mapper.readValue(staff.getContent(), 
                new TypeReference<List<HrSelfAssessExcel>>(){});
            
            BigDecimal benefitScore = BigDecimal.ZERO;
            for (HrSelfAssessExcel item : list) {
                if (item.getCategory() != null && item.getCategory().contains("效益") 
                    && StringUtils.isNotEmpty(item.getDePoints())) {
                    try {
                        BigDecimal score = new BigDecimal(item.getDePoints());
                        benefitScore = benefitScore.add(score);
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的数字
                    }
                }
            }
            return benefitScore;
        } catch (Exception e) {
            log.warn("计算效益得分失败，工号：" + staff.getWorkNo(), e);
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 提取申请加分事项
     */
    private String extractAddPointsItems(String content) {
        try {
            if (StringUtils.isEmpty(content)) {
                return "";
            }
            
            ObjectMapper mapper = new ObjectMapper();
            List<HrSelfAssessExcel> list = mapper.readValue(content, 
                new TypeReference<List<HrSelfAssessExcel>>(){});
            
            StringBuilder items = new StringBuilder();
            for (HrSelfAssessExcel item : list) {
                // 查找item字段为'加分项'的行，取category字段作为申请加分事项
                if ("加分项".equals(item.getItem()) && StringUtils.isNotEmpty(item.getPerformance())) {
                    if (items.length() > 0) {
                        items.append("；");
                    }
                    items.append(item.getPerformance());
                }
            }
            return items.toString();
        } catch (Exception e) {
            log.warn("提取申请加分事项失败", e);
            return "";
        }
    }

    /**
     * 提取并汇总所有加减分理由
     * 包括：自评加减分理由、部门领导加减分理由、事业部加减分理由、运改组织加减分理由
     */
    private String extractAllReasons(HrSelfAssessInfo staff) {
        StringBuilder allReasons = new StringBuilder();
        
        try {
            // 1. 提取自评加减分理由（从content中解析）
            String selfReasons = extractSelfReasons(staff.getContent());
            if (StringUtils.isNotEmpty(selfReasons)) {
                allReasons.append(selfReasons);
            }
            
            // 2. 部门领导加减分理由
            if (StringUtils.isNotEmpty(staff.getDeptScoreReason())) {
                if (allReasons.length() > 0) {
                    allReasons.append("\n");
                }
                allReasons.append(staff.getDeptScoreReason());
            }
            
            // 3. 事业部加减分理由
            if (StringUtils.isNotEmpty(staff.getBusinessScoreReason())) {
                if (allReasons.length() > 0) {
                    allReasons.append("\n");
                }
                allReasons.append(staff.getBusinessScoreReason());
            }
            
            // 4. 运改组织加减分理由
            if (StringUtils.isNotEmpty(staff.getOrganizationScoreReason())) {
                if (allReasons.length() > 0) {
                    allReasons.append("\n");
                }
                allReasons.append(staff.getOrganizationScoreReason());
            }
            
            // 5. 条线领导加减分理由
            if (StringUtils.isNotEmpty(staff.getLeaderReview())) {
                if (allReasons.length() > 0) {
                    allReasons.append("\n");
                }
                allReasons.append(staff.getLeaderReview());
            }
            
        } catch (Exception e) {
            log.warn("提取加减分理由失败，工号：" + staff.getWorkNo(), e);
        }
        
        return allReasons.toString();
    }

    /**
     * 从content中提取自评加减分理由
     */
    private String extractSelfReasons(String content) {
        try {
            if (StringUtils.isEmpty(content)) {
                return "";
            }
            
            ObjectMapper mapper = new ObjectMapper();
            List<HrSelfAssessExcel> list = mapper.readValue(content, 
                new TypeReference<List<HrSelfAssessExcel>>(){});
            
            StringBuilder reasons = new StringBuilder();
            for (HrSelfAssessExcel item : list) {
                // 提取有加减分理由的项目
                if (StringUtils.isNotEmpty(item.getPointsReason())) {
                    if (reasons.length() > 0) {
                        reasons.append("，");
                    }
                    reasons.append(item.getPointsReason());
                }
            }
            return reasons.toString();
        } catch (Exception e) {
            log.warn("提取自评加减分理由失败", e);
            return "";
        }
    }

    @Override
    public String exportAdministrativePerformanceSummary(HrSelfAssessInfo hrSelfAssessInfo) {
        OutputStream out = null;
        XSSFWorkbook wb = null;
        String filename = null;
        
        try {
            // 查询有行政序列干部的条线领导列表
            List<HrSelfAssessUser> leaders = hrSelfAssessInfoMapper.selectLeadersWithAdministrativeStaff(hrSelfAssessInfo);
            
            if (leaders.isEmpty()) {
                throw new CustomException("该考核年月没有找到行政序列干部数据！");
            }
            
            wb = new XSSFWorkbook();
            
            // 为每个条线领导创建一个Sheet
            for (HrSelfAssessUser leader : leaders) {
                // 查询该条线领导下的行政序列干部
                HrSelfAssessInfo queryInfo = new HrSelfAssessInfo();
                queryInfo.setAssessDate(hrSelfAssessInfo.getAssessDate());
                queryInfo.setLeaderNo(leader.getWorkNo());
                
                List<HrSelfAssessInfo> administrativeStaffList = hrSelfAssessInfoMapper.selectAdministrativeStaffByLeader(queryInfo);
                
                if (!administrativeStaffList.isEmpty()) {
                    createAdministrativeSummarySheet(wb, leader, administrativeStaffList, hrSelfAssessInfo.getAssessDate());
                }
            }
            
            // 生成文件名
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年M月");
            String assessDateStr = dateFormat.format(hrSelfAssessInfo.getAssessDate());
            filename = UUID.randomUUID().toString() + "_" + assessDateStr + "行政序列业绩汇总表.xlsx";
            String downloadPath = RuoYiConfig.getDownloadPath() + filename;
            File desc = new File(downloadPath);
            if (!desc.getParentFile().exists()) {
                desc.getParentFile().mkdirs();
            }
            
            out = new FileOutputStream(downloadPath);
            wb.write(out);
            
        } catch (Exception e) {
            log.error("导出行政序列业绩汇总表失败", e);
            throw new CustomException("导出行政序列业绩汇总表失败，请联系管理员！");
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                    log.error("导出行政序列业绩汇总表失败", e1);
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    log.error("导出行政序列业绩汇总表失败", e1);
                }
            }
        }
        
        return filename;
    }
    
    /**
     * 创建行政序列业绩汇总表Sheet
     */
    private void createAdministrativeSummarySheet(XSSFWorkbook wb, HrSelfAssessUser leader, 
                                                List<HrSelfAssessInfo> staffList, Date assessDate) {
        
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年M月");
        String assessDateStr = dateFormat.format(assessDate);
        
        // 创建Sheet，名称为条线领导姓名
        XSSFSheet sheet = wb.createSheet(leader.getName());
        
        // 设置列宽
        sheet.setColumnWidth(0, 8 * 256);   // 序号
        sheet.setColumnWidth(1, 16 * 256);  // 部门
        sheet.setColumnWidth(2, 12 * 256);  // 姓名
        sheet.setColumnWidth(3, 16 * 256);  // 职务
        sheet.setColumnWidth(4, 12 * 256);  // 自评分
        sheet.setColumnWidth(5, 16 * 256);  // 部门领导评分
        sheet.setColumnWidth(6, 16 * 256);  // 事业部评分
        sheet.setColumnWidth(7, 18 * 256);  // 运改/组织审核得分
        sheet.setColumnWidth(8, 18 * 256);  // 挂钩公司效益得分
        sheet.setColumnWidth(9, 20 * 256);  // 申请加分事项
        sheet.setColumnWidth(10, 12 * 256); // 加分值
        sheet.setColumnWidth(11, 18 * 256); // 总经理部领导评分
        sheet.setColumnWidth(12, 16 * 256); // 备注
        
        // 创建标题样式
        CellStyle titleStyle = wb.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = wb.createFont();
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setBold(true);
        titleStyle.setFont(titleFont);
        
        // 创建表头样式
        CellStyle headerStyle = setCellStyle(wb.createCellStyle());
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font headerFont = wb.createFont();
        headerFont.setFontHeightInPoints((short) 11);
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        
        // 创建数据样式
        CellStyle dataStyle = setCellStyle(wb.createCellStyle());
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setWrapText(true);
        
        CellStyle numberStyle = setCellStyle(wb.createCellStyle());
        numberStyle.setAlignment(HorizontalAlignment.CENTER);
        numberStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 第一行：标题
        XSSFRow titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(24);
        XSSFCell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(assessDateStr + "行政序列业绩汇总表（" + leader.getName() + "总）");
        titleCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 12));
        
        // 第二行：表头
        XSSFRow headerRow = sheet.createRow(1);
        headerRow.setHeightInPoints(20);
        String[] headers = {
            "序号", "部门", "姓名", "职务", "自评分", "部门领导评分", 
            "事业部评分", "运改/组织审核得分", "挂钩公司效益得分", "申请加分事项（封顶加5分）", 
            "加分值", "总经理部领导评分", "备注"
        };
        
        for (int i = 0; i < headers.length; i++) {
            XSSFCell headerCell = headerRow.createCell(i);
            headerCell.setCellValue(headers[i]);
            headerCell.setCellStyle(headerStyle);
        }
        
        // 数据行
        int rowIndex = 2;
        for (int i = 0; i < staffList.size(); i++) {
            HrSelfAssessInfo staff = staffList.get(i);
            XSSFRow dataRow = sheet.createRow(rowIndex++);
            dataRow.setHeightInPoints(18);
            
            // 序号
            XSSFCell seqCell = dataRow.createCell(0);
            seqCell.setCellValue(i + 1);
            seqCell.setCellStyle(numberStyle);
            
            // 部门
            XSSFCell deptCell = dataRow.createCell(1);
            deptCell.setCellValue(staff.getDeptName() != null ? staff.getDeptName() : "");
            deptCell.setCellStyle(dataStyle);
            
            // 姓名
            XSSFCell nameCell = dataRow.createCell(2);
            nameCell.setCellValue(staff.getName() != null ? staff.getName() : "");
            nameCell.setCellStyle(dataStyle);
            
            // 职务
            XSSFCell jobCell = dataRow.createCell(3);
            jobCell.setCellValue(staff.getJob() != null ? staff.getJob() : "");
            jobCell.setCellStyle(dataStyle);
            
            // 自评分
            XSSFCell selfScoreCell = dataRow.createCell(4);
            if (staff.getSelfScore() != null) {
                selfScoreCell.setCellValue(staff.getSelfScore().doubleValue());
            }
            selfScoreCell.setCellStyle(numberStyle);
            
            // 部门领导评分
            XSSFCell deptScoreCell = dataRow.createCell(5);
            if (staff.getDeptScore() != null) {
                deptScoreCell.setCellValue(staff.getDeptScore().doubleValue());
            }
            deptScoreCell.setCellStyle(numberStyle);
            
            // 事业部评分
            XSSFCell businessScoreCell = dataRow.createCell(6);
            if (staff.getBusinessScore() != null) {
                businessScoreCell.setCellValue(staff.getBusinessScore().doubleValue());
            }
            businessScoreCell.setCellStyle(numberStyle);
            
            // 运改/组织审核得分
            XSSFCell orgScoreCell = dataRow.createCell(7);
            if (staff.getOrganizationScore() != null) {
                orgScoreCell.setCellValue(staff.getOrganizationScore().doubleValue());
            }
            orgScoreCell.setCellStyle(numberStyle);
            
            // 挂钩公司效益得分
            XSSFCell benefitScoreCell = dataRow.createCell(8);
            if (staff.getOrganizationScore() != null) {
                benefitScoreCell.setCellValue(staff.getOrganizationScore().doubleValue());
            }
            benefitScoreCell.setCellStyle(numberStyle);
            
            // 申请加分事项（从content中解析）
            XSSFCell addPointsItemCell = dataRow.createCell(9);
            String addPointsItems = extractAddPointsItems(staff.getContent());
            addPointsItemCell.setCellValue(addPointsItems);
            addPointsItemCell.setCellStyle(dataStyle);
            
            // 加分值（设置为空字符串）
            XSSFCell addPointsValueCell = dataRow.createCell(10);
            addPointsValueCell.setCellValue("");
            addPointsValueCell.setCellStyle(numberStyle);
            
            // 总经理部领导评分
            XSSFCell leaderScoreCell = dataRow.createCell(11);
            if (staff.getFinalScore() != null) {
                leaderScoreCell.setCellValue(staff.getFinalScore().doubleValue());
            }
            leaderScoreCell.setCellStyle(numberStyle);
            
            // 备注 - 汇总所有加减分理由
            XSSFCell remarkCell = dataRow.createCell(12);
            String remarkContent = extractAllReasons(staff);
            remarkCell.setCellValue(remarkContent);
            remarkCell.setCellStyle(dataStyle);
        }
    }
    

}
