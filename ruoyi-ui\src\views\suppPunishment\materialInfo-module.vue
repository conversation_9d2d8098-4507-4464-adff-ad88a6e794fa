<template>
  <el-dialog
    title="物料信息查询"
    :visible.sync="visible"
    width="900px"
    append-to-body
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px">
      <el-form-item label="物料小类代码" prop="itemId">
        <el-input
          v-model="queryParams.itemId"
          placeholder="请输入物料小类代码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料小类名称" prop="itemName">
        <el-input
          v-model="queryParams.itemName"
          placeholder="请输入物料小类名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item >
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 物料列表 -->
    <el-table 
      v-loading="loading" 
      :data="materialList" 
      @row-click="handleRowClick"
      @row-dblclick="handleRowDoubleClick"
      highlight-current-row
      style="cursor: pointer;"
    >
      <el-table-column label="物料小类代码" align="center" prop="itemId" width="150" />
      <el-table-column label="物料小类名称" align="center" prop="itemName" />
      <el-table-column label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleSelect(scope.row)"
          >选择</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { listMaterialInfo } from "@/api/suppPunishment/materialInfo";

export default {
  name: "MaterialInfoDialog",
  data() {
    return {
      // 是否显示弹出层
      visible: false,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 物料信息表格数据
      materialList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        itemId: null,
        itemName: null
      }
    };
  },
  methods: {
    /** 显示弹窗 */
    show() {
      this.visible = true;
      this.resetQuery();
      this.getList();
    },
    
    /** 隐藏弹窗 */
    hide() {
      this.visible = false;
    },
    
    /** 关闭弹窗 */
    handleClose() {
      this.visible = false;
      this.reset();
    },
    
    /** 重置数据 */
    reset() {
      this.materialList = [];
      this.total = 0;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        itemId: null,
        itemName: null
      };
    },
    
    /** 查询物料信息列表 */
    getList() {
      this.loading = true;
      listMaterialInfo(this.queryParams).then(response => {
        this.materialList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    /** 行点击事件 */
    handleRowClick(row) {
      // 可以在这里添加行选中效果
    },
    
    /** 行双击事件 */
    handleRowDoubleClick(row) {
      this.handleSelect(row);
    },
    
    /** 选择物料 */
    handleSelect(row) {
      this.$emit('select', row);
      this.handleClose();
    }
  }
};
</script>

<style scoped>
/* 表格行悬停效果 */
::v-deep .el-table tbody tr:hover {
  background-color: #f5f7fa;
}

/* 查询表单样式 - 输入框左对齐，按钮右对齐 */
.el-form--inline {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

/* 输入框区域左对齐 */
.el-form--inline .el-form-item:not(:last-child) {
  margin-right: 15px;
  margin-bottom: 0;
}

/* 按钮区域右对齐 */
.el-form--inline .el-form-item:last-child {
  margin-left: auto;
  margin-right: 0;
  margin-bottom: 0;
}

/* 统一输入框宽度 */
.el-form--inline .el-form-item .el-input {
  width: 200px;
}

/* 弹窗标题居中 */
::v-deep .el-dialog__header {
  text-align: center;
}

::v-deep .el-dialog__title {
  text-align: center;
  width: 100%;
  display: block;
}
</style>
