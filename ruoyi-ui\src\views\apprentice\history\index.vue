<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="工号" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入工号" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="入职年份" prop="employYear">
        <el-date-picker
          v-model="queryParams.employYear"
          type="year"
          placeholder="请选择入职年份"
          value-format="yyyy"
          clearable
          size="small">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option label="全部" value="" />
          <el-option label="结业" value="1" />
          <el-option label="离职" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="success" icon="el-icon-download" size="mini" @click="handleExportByYear">汇总导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="historyList">
      <el-table-column label="入职年份" align="center" prop="employYear" />
      <el-table-column label="工号" align="center" prop="userName" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="年龄" align="center" prop="age" />
      <el-table-column label="学历" align="center" prop="education" />
      <el-table-column label="作业区/科室" align="center" prop="office" />
      <el-table-column label="岗位" align="center" prop="post" />
      <el-table-column label="以师带徒期限" align="center">
        <template slot-scope="scope">
          {{ formatDeadline(scope.row.deadline) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          {{ formatStatus(scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 查看历史人员详情对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="工号">
              <span>{{ form.userName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名">
              <span>{{ form.name }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="出生日期">
              <span>{{ form.birthday }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年龄">
              <span>{{ form.age }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="入职年份">
              <span>{{ form.employYear }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学历">
              <span>{{ form.education }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="毕业院校及专业">
              <span>{{ form.profession }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="作业区/科室">
              <span>{{ form.office }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位">
              <span>{{ form.post }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="以师带徒期限">
              <span>{{ formatDeadline(form.deadline) }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-tag :type="getStatusType(form.status)">{{ formatStatus(form.status) }}</el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="班组评价人">
              <span>{{ form.teamEvaluateUser }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作导师评价人">
              <span>{{ form.supervisorEvaluateUser }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="人力资源部评价人">
              <span>{{ form.hrEvaluateUser }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="企业导师评价人">
              <span>{{ form.leaderEvaluateUser }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="创建时间">
              <span>{{ parseTime(form.createTime) }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="更新时间">
              <span>{{ parseTime(form.updateTime) }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInfo, getInfo, exportByEmployYear } from "@/api/apprentice/info";
import { getDicts } from "@/api/system/dict/data";

export default {
  name: "ApprenticeHistory",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 历史人员表格数据
      historyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: null,
        name: null,
        status: "", // 默认为空，显示"全部"
        employYear: null,
      },
      // 表单参数
      form: {},
      // 期限选择项
      deadlineOptions: [],
      // 状态选择项
      statusOptions: [
        { label: '结业', value: '1' },
        { label: '离职', value: '2' }
      ]
    };
  },
  created() {
    this.getDicts("apprentice_deadline").then(response => {
      this.deadlineOptions = response.data;
    });
    this.getList();
  },
  methods: {
    /** 查询历史人员列表 */
    getList() {
      this.loading = true;

      // 分别查询结业和离职人员
      const promises = [];

      if (this.queryParams.status === "" || !this.queryParams.status) {
        // 选择"全部"时，分别查询结业和离职人员
        const params1 = { ...this.queryParams, status: '1' }; // 结业
        const params2 = { ...this.queryParams, status: '2' }; // 离职
        promises.push(listInfo(params1));
        promises.push(listInfo(params2));
      } else {
        // 选择具体状态时，只查询该状态
        const params = { ...this.queryParams };
        promises.push(listInfo(params));
      }

      Promise.all(promises).then(responses => {
        let allData = [];
        responses.forEach(response => {
          if (response.rows && response.rows.length > 0) {
            allData = allData.concat(response.rows);
          }
        });

        // 确保只显示结业和离职人员
        this.historyList = allData.filter(item =>
          item.status === '1' || item.status === '2'
        );

        // 按更新时间倒序排列
        this.historyList.sort((a, b) => {
          return new Date(b.updateTime) - new Date(a.updateTime);
        });

        this.total = this.historyList.length;
        this.loading = false;
      }).catch(error => {
        console.error('获取历史人员列表失败:', error);
        this.$message.error('获取历史人员列表失败');
        this.loading = false;
      });
    },
    
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userName: null,
        name: null,
        age: null,
        education: null,
        profession: null,
        office: null,
        post: null,
        deadline: null,
        employYear: null,
        status: null,
        teamEvaluateUser: null,
        supervisorEvaluateUser: null,
        hrEvaluateUser: null,
        leaderEvaluateUser: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      // 重置后状态默认为空（显示"全部"）
      this.queryParams.status = "";
      this.handleQuery();
    },
    
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id;
      getInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "查看历史人员详情";
      });
    },
    


    /** 汇总导出月度跟踪记录 */
    handleExportByYear() {
      // 获取当前查询条件
      const currentQuery = { ...this.queryParams };

      // 确保只导出历史人员
      if (currentQuery.status === "" || !currentQuery.status) {
        currentQuery.status = '1,2'; // 全部历史人员
      }

      // 构建查询条件描述
      let queryDesc = "当前查询条件下的人员";
      if (currentQuery.employYear) {
        queryDesc = `${currentQuery.employYear}年入职的人员`;
      }
      if (currentQuery.name) {
        queryDesc += `（姓名包含"${currentQuery.name}"）`;
      }
      if (currentQuery.userName) {
        queryDesc += `（工号包含"${currentQuery.userName}"）`;
      }

      // 确认导出
      this.$confirm(`是否确认汇总导出${queryDesc}的月度跟踪记录？`, "确认汇总导出", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        // 显示加载提示
        const loading = this.$loading({
          lock: true,
          text: '正在生成导出文件，请稍候...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 调用导出接口，传递查询条件
        exportByEmployYear(null, currentQuery).then(response => {
          loading.close();

          // 检查响应是否为空或过小（可能是错误响应）
          if (!response || response.size < 100) {
            this.$message.error('没有找到符合条件的人员记录或数据为空');
            return;
          }

          // 检查响应类型，如果不是zip文件可能是错误响应
          if (response.type && response.type.includes('json')) {
            // 解析JSON错误信息
            const reader = new FileReader();
            reader.onload = () => {
              try {
                const errorData = JSON.parse(reader.result);
                this.$message.error(errorData.msg || '没有找到符合条件的人员记录');
              } catch (e) {
                this.$message.error('没有找到符合条件的人员记录');
              }
            };
            reader.readAsText(response);
            return;
          }

          // 处理文件下载
          const blob = new Blob([response], {
            type: 'application/zip'
          });

          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;

          // 生成文件名
          let fileName = "历史人员月度跟踪记录";
          if (currentQuery.employYear) {
            fileName = `${currentQuery.employYear}年入职人员月度跟踪记录`;
          }
          link.download = `${fileName}.zip`;

          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          this.$message.success('汇总导出成功');

        }).catch(error => {
          loading.close();
          console.error('导出失败:', error);

          if (error.response && error.response.status === 404) {
            this.$message.error('没有找到符合条件的人员记录');
          } else if (error.response && error.response.data) {
            // 尝试解析错误信息
            try {
              const reader = new FileReader();
              reader.onload = () => {
                try {
                  const errorData = JSON.parse(reader.result);
                  this.$message.error(errorData.msg || '导出失败');
                } catch (e) {
                  this.$message.error('导出失败，请稍后重试');
                }
              };
              reader.readAsText(error.response.data);
            } catch (e) {
              this.$message.error('导出失败，请稍后重试');
            }
          } else {
            this.$message.error('导出失败，请稍后重试');
          }
        });
      }).catch(() => {
        // 用户取消导出
      });
    },
    
    /** 格式化期限显示 */
    formatDeadline(deadline) {
      if (!deadline) return '';
      const option = this.deadlineOptions.find(item => item.dictValue === deadline);
      return option ? option.dictLabel : deadline;
    },
    
    /** 格式化状态显示 */
    formatStatus(status) {
      const statusMap = {
        '0': '正常',
        '1': '结业',
        '2': '离职'
      };
      return statusMap[status] || '未知';
    },
    
    /** 获取状态标签类型 */
    getStatusType(status) {
      const typeMap = {
        '0': 'success',
        '1': 'primary',
        '2': 'info'
      };
      return typeMap[status] || 'info';
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
