package com.ruoyi.app.supp.mapper;

import java.util.List;

import com.ruoyi.app.domain.MaterialInfo;
import com.ruoyi.app.supp.domain.ServiceProject;
import com.ruoyi.app.supp.domain.SuppInfo;
import com.ruoyi.app.supp.domain.SuppPunishment;
import com.ruoyi.app.supp.domain.SuppUser;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 供应商处罚Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface SuppPunishmentMapper
{
    /**
     * 查询供应商处罚
     *
     * @param id 供应商处罚ID
     * @return 供应商处罚
     */
    public SuppPunishment selectSuppPunishmentById(Long id);

    /**
     * 查询供应商处罚列表
     *
     * @param suppPunishment 供应商处罚
     * @return 供应商处罚集合
     */
    public List<SuppPunishment> selectSuppPunishmentList(SuppPunishment suppPunishment);

    /**
     * 新增供应商处罚
     *
     * @param suppPunishment 供应商处罚
     * @return 结果
     */
    public int insertSuppPunishment(SuppPunishment suppPunishment);

    /**
     * 修改供应商处罚
     *
     * @param suppPunishment 供应商处罚
     * @return 结果
     */
    public int updateSuppPunishment(SuppPunishment suppPunishment);

    /**
     * 删除供应商处罚
     *
     * @param id 供应商处罚ID
     * @return 结果
     */
    public int deleteSuppPunishmentById(Long id);

    /**
     * 批量删除供应商处罚
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSuppPunishmentByIds(Long[] ids);

    @DataSource(value = DataSourceType.XCC1)
    List<SuppInfo> selectSuppInfoList(SuppInfo suppInfo);

    @DataSource(value = DataSourceType.XCC1)
    List<MaterialInfo> selectMaterialInfoList(MaterialInfo materialInfo);

    /**
     * 确认
     *
     * @param  suppPunishment 供应商处罚
     * @return 结果
     */
    public void confirmSuppPunishmentById(SuppPunishment suppPunishment);

    List<ServiceProject> selectServiceList(ServiceProject serviceProject);
    List<ServiceProject> selectProjectList(ServiceProject serviceProject);

    public SuppPunishment selectSuppPunishmentSerialNo();

    SuppUser selectUserGroupByUserName(SuppUser suppUser);

}
