package com.ruoyi.app.qualityCost.service.impl;


import com.ruoyi.app.qualityCost.domain.CompanySummaryData;
import com.ruoyi.app.qualityCost.domain.QualityCostDetail;
import com.ruoyi.app.qualityCost.mapper.CompanySummaryMapper;
import com.ruoyi.app.qualityCost.service.ICompanySummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Service
public class CompanySummaryServiceImpl implements ICompanySummaryService {


    @Autowired
    private CompanySummaryMapper companySummaryMapper;

    @Override
    public HashMap<String, HashMap<Integer, CompanySummaryData>> listCompanySummaryData(QualityCostDetail qualityCostDetail) {

        Integer containType = qualityCostDetail.getContainType();

        List<String> sortOrder = Arrays.asList(
                "试验检测所", "一炼", "一轧", "二炼", "二轧大棒",
                "二轧小棒", "三轧钢", "高线", "杨市棒扁", "杨市盘卷",
                "银亮材", "线材深加工", "特板厂炼钢", "厚板",
                "中板", "钢板深加工", "三炼钢","公司"
        );

        LinkedHashMap<String, HashMap<Integer, CompanySummaryData>> result = new LinkedHashMap<>();

        for (String costCenterName : sortOrder) {
            HashMap<Integer, CompanySummaryData> monthValueMap = new HashMap<>();

            QualityCostDetail newCondition = new QualityCostDetail();
            newCondition.setCostCenterCname(costCenterName);
            newCondition.setYearMonth(qualityCostDetail.getYearMonth());

            List<QualityCostDetail> qualityCostDetailList = companySummaryMapper.selectQualityCostDetailByYear(newCondition);

            CompanySummaryData defaultValue = new CompanySummaryData();
            defaultValue.setCostEx(BigDecimal.ZERO);
            defaultValue.setCostTon(BigDecimal.ZERO);
            monthValueMap.put(1, defaultValue);
            monthValueMap.put(2, defaultValue);
            monthValueMap.put(3, defaultValue);
            monthValueMap.put(4, defaultValue);
            monthValueMap.put(5, defaultValue);
            monthValueMap.put(6, defaultValue);
            monthValueMap.put(7, defaultValue);
            monthValueMap.put(8, defaultValue);
            monthValueMap.put(9, defaultValue);
            monthValueMap.put(10, defaultValue);
            monthValueMap.put(11, defaultValue);
            monthValueMap.put(12, defaultValue);

            for (QualityCostDetail detail : qualityCostDetailList) {

                if (qualityCostDetail.getCompanySummaryType().equals("改判")) {
                    if (detail.getTypeCode().equals("C2")) {
                        monthValueMap = this.handleMonthValueMap(monthValueMap, detail,containType);
                    }
                } else if (qualityCostDetail.getCompanySummaryType().equals("报废")) {
                    if (detail.getTypeCode().equals("C1")) {
                        monthValueMap = this.handleMonthValueMap(monthValueMap, detail,containType);
                    }
                } else if (qualityCostDetail.getCompanySummaryType().equals("脱合同")) {
                    if (detail.getTypeCode().equals("C3")) {
                        monthValueMap = this.handleMonthValueMap(monthValueMap, detail,containType);
                    }
                } else if (qualityCostDetail.getCompanySummaryType().equals("退货")) {
                    if (detail.getTypeCode().equals("D1")) {
                        monthValueMap = this.handleMonthValueMap(monthValueMap, detail,containType);
                    }
                } else {
                    return null;
                }
            }

            result.put(costCenterName, monthValueMap);
        }


        return result;
    }

    private HashMap<Integer, CompanySummaryData> handleMonthValueMap(HashMap<Integer, CompanySummaryData> monthValueMap, QualityCostDetail qualityCostDetail,Integer containType) {


        CompanySummaryData companySummaryData = new CompanySummaryData();
        if (containType == 1) {
            companySummaryData.setCostEx(qualityCostDetail.getCostEx());
            companySummaryData.setCostTon(qualityCostDetail.getCostTon());
        } else {
            companySummaryData.setCostEx(qualityCostDetail.getAllcEx());
            companySummaryData.setCostTon(qualityCostDetail.getAllcTon());
        }

        String month = qualityCostDetail.getYearMonth().substring(qualityCostDetail.getYearMonth().length() - 2);
        if (month.equals("01")) {
            monthValueMap.put(1, companySummaryData);
        }
        if (month.equals("02")) {
            monthValueMap.put(2, companySummaryData);
        }
        if (month.equals("03")) {
            monthValueMap.put(3, companySummaryData);
        }
        if (month.equals("04")) {
            monthValueMap.put(4, companySummaryData);
        }
        if (month.equals("05")) {
            monthValueMap.put(5, companySummaryData);
        }
        if (month.equals("06")) {
            monthValueMap.put(6, companySummaryData);
        }
        if (month.equals("07")) {
            monthValueMap.put(7, companySummaryData);
        }
        if (month.equals("08")) {
            monthValueMap.put(8, companySummaryData);
        }
        if (month.equals("09")) {
            monthValueMap.put(9, companySummaryData);
        }
        if (month.equals("10")) {
            monthValueMap.put(10, companySummaryData);
        }
        if (month.equals("11")) {
            monthValueMap.put(11, companySummaryData);
        }
        if (month.equals("12")) {
            monthValueMap.put(12, companySummaryData);
        }

        return monthValueMap;
    }
}
