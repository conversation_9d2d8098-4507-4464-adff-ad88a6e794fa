import request from '@/utils/request'

// 查询外部损失成本-质量异议退货损失列表
export function listQualityObjection(query) {
  return request({
    url: '/qualityCost/qualityObjection/list',
    method: 'get',
    params: query
  })
}

// 查询外部损失成本-质量异议退货损失详细
export function getQualityObjection(recCreator) {
  return request({
    url: '/qualityCost/qualityObjection/' + recCreator,
    method: 'get'
  })
}

// 新增外部损失成本-质量异议退货损失
export function addQualityObjection(data) {
  return request({
    url: '/qualityCost/qualityObjection',
    method: 'post',
    data: data
  })
}

// 修改外部损失成本-质量异议退货损失
export function updateQualityObjection(data) {
  return request({
    url: '/qualityCost/qualityObjection',
    method: 'put',
    data: data
  })
}

// 删除外部损失成本-质量异议退货损失
export function delQualityObjection(recCreator) {
  return request({
    url: '/qualityCost/qualityObjection/' + recCreator,
    method: 'delete'
  })
}

// 导出外部损失成本-质量异议退货损失
export function exportQualityObjection(query) {
  return request({
    url: '/qualityCost/qualityObjection/export',
    method: 'get',
    params: query
  })
}

// 查询内部损失成本-产品报废损失明细列表（全量）
export function listAllQualityObjection(query) {
  return request({
    url: '/qualityCost/qualityObjection/listAll',
    method: 'get',
    params: query
  })
}

export function getSum(query) {
  return request({
    url: '/qualityCost/qualityObjection/getSum',
    method: 'get',
    params: query
  })
}

export function getAllSum(query) {
  return request({
    url: '/qualityCost/qualityObjection/getAllSum',
    method: 'get',
    params: query
  })
}
