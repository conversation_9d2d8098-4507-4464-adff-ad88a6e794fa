import request from '@/utils/request'

// 查询内部损失成本-产品改判损失明细列表
export function listRegradeDetail(query) {
  return request({
    url: '/qualityCost/regradeDetail/list',
    method: 'get',
    params: query
  })
}

// 查询内部损失成本-产品改判损失明细列表（全量）
export function listAllRegradeDetail(query) {
  return request({
    url: '/qualityCost/regradeDetail/listAll',
    method: 'get',
    params: query
  })
}

// 查询内部损失成本-产品改判损失明细详细
export function getRegradeDetail(recCreator) {
  return request({
    url: '/qualityCost/regradeDetail/' + recCreator,
    method: 'get'
  })
}

// 新增内部损失成本-产品改判损失明细
export function addRegradeDetail(data) {
  return request({
    url: '/qualityCost/regradeDetail',
    method: 'post',
    data: data
  })
}

// 修改内部损失成本-产品改判损失明细
export function updateRegradeDetail(data) {
  return request({
    url: '/qualityCost/regradeDetail',
    method: 'put',
    data: data
  })
}

// 删除内部损失成本-产品改判损失明细
export function delRegradeDetail(recCreator) {
  return request({
    url: '/qualityCost/regradeDetail/' + recCreator,
    method: 'delete'
  })
}

// 导出内部损失成本-产品改判损失明细
export function exportRegradeDetail(query) {
  return request({
    url: '/qualityCost/regradeDetail/export',
    method: 'get',
    params: query
  })
}

export function getSum(query) {
  return request({
    url: '/qualityCost/regradeDetail/getSum',
    method: 'get',
    params: query
  })
}

export function getAllSum(query) {
  return request({
    url: '/qualityCost/regradeDetail/getAllSum',
    method: 'get',
    params: query
  })
}

