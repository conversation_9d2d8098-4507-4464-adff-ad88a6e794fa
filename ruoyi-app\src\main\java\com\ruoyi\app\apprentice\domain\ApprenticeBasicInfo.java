package com.ruoyi.app.apprentice.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 以师带徒基本信息对象 apprentice_basic_info
 *
 * <AUTHOR>
 * @date 2023-08-29
 */
public class ApprenticeBasicInfo
{
    private static final long serialVersionUID = 1L;

    /** 基本信息id */
    private Long id;

    /** 工号 */
    @Excel(name = "工号")
    private String userName;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 出生年月（yyyy-MM-dd） */
    @Excel(name = "出生日期（yyyy-MM-dd）")
    private String birthday;

    /** 年龄 */
    @Excel(name = "年龄")
    private Integer age;

    /** 学历 */
    @Excel(name = "学历")
    private String education;

    /** 毕业院校及专业 */
    @Excel(name = "毕业院校及专业")
    private String profession;

    /** 作业区/科室 */
    @Excel(name = "作业区/科室")
    private String office;

    /** 岗位 */
    @Excel(name = "岗位")
    private String post;

    /** 以师带徒期限 */
    @Excel(name = "以师带徒期限（3:3个月/24:2年）")
    private Integer deadline;

    /** 入职年份 */
    @Excel(name = "入职年份")
    private String employYear;

    private String delFlag;

    /** 状态 */
    private String status;

    /** 班组评价人 */
    @Excel(name = "班组评价人工号")
    private String teamEvaluateUser;

    /** 工作导师评价人 */
    @Excel(name = "工作导师评价人工号")
    private String supervisorEvaluateUser;

    /** 人力资源部评价人 */
    @Excel(name = "人力资源部评价人工号")
    private String hrEvaluateUser;

    /** 企业导师评价人 */
    @Excel(name = "企业导师评价人工号")
    private String leaderEvaluateUser;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String remark;

    @Excel(name = "导入结果（不填）")
    private String result;

    @Excel(name = "失败原因（不填）")
    private String reason;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    public String getUserName()
    {
        return userName;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setAge(Integer age)
    {
        this.age = age;
    }

    public Integer getAge()
    {
        return age;
    }
    public void setEducation(String education)
    {
        this.education = education;
    }

    public String getEducation()
    {
        return education;
    }
    public void setProfession(String profession)
    {
        this.profession = profession;
    }

    public String getProfession()
    {
        return profession;
    }
    public void setOffice(String office)
    {
        this.office = office;
    }

    public String getOffice()
    {
        return office;
    }
    public void setPost(String post)
    {
        this.post = post;
    }

    public String getPost()
    {
        return post;
    }
    public void setDeadline(Integer deadline)
    {
        this.deadline = deadline;
    }

    public Integer getDeadline()
    {
        return deadline;
    }
    public void setEmployYear(String employYear)
    {
        this.employYear = employYear;
    }

    public String getEmployYear()
    {
        return employYear;
    }
    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setTeamEvaluateUser(String teamEvaluateUser)
    {
        this.teamEvaluateUser = teamEvaluateUser.trim();
    }

    public String getTeamEvaluateUser()
    {
        return teamEvaluateUser;
    }
    public void setSupervisorEvaluateUser(String supervisorEvaluateUser)
    {
        this.supervisorEvaluateUser = supervisorEvaluateUser.trim();
    }

    public String getSupervisorEvaluateUser()
    {
        return supervisorEvaluateUser;
    }
    public void setHrEvaluateUser(String hrEvaluateUser)
    {
        this.hrEvaluateUser = hrEvaluateUser.trim();
    }

    public String getHrEvaluateUser()
    {
        return hrEvaluateUser;
    }
    public void setLeaderEvaluateUser(String leaderEvaluateUser)
    {
        this.leaderEvaluateUser = leaderEvaluateUser.trim();
    }

    public String getLeaderEvaluateUser()
    {
        return leaderEvaluateUser;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userName", getUserName())
                .append("name", getName())
                .append("age", getAge())
                .append("education", getEducation())
                .append("profession", getProfession())
                .append("office", getOffice())
                .append("post", getPost())
                .append("deadline", getDeadline())
                .append("employYear", getEmployYear())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .append("status", getStatus())
                .append("teamEvaluateUser", getTeamEvaluateUser())
                .append("supervisorEvaluateUser", getSupervisorEvaluateUser())
                .append("hrEvaluateUser", getHrEvaluateUser())
                .append("leaderEvaluateUser", getLeaderEvaluateUser())
                .toString();
    }
}