<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.v1.mapper.StaticAnswerMapper">
    
    <resultMap type="StaticAnswer" id="StaticAnswerResult">
        <result property="id"    column="id"    />
        <result property="dimensionalityId"    column="dimensionality_id"    />
        <result property="dimensionalityPath"    column="dimensionality_path"    />
        <result property="dimensionalityName0"    column="dimensionality_name0"    />
        <result property="dimensionalityName1"    column="dimensionality_name1"    />
        <result property="dimensionalityName2"    column="dimensionality_name2"    />
        <result property="dimensionalityName3"    column="dimensionality_name3"    />
        <result property="dimensionalityName4"    column="dimensionality_name4"    />
        <result property="dimensionalityName5"    column="dimensionality_name5"    />
        <result property="formId"    column="form_id"    />
        <result property="formQuestion"    column="form_question"    />
        <result property="formUnit"    column="form_unit"    />
        <result property="formNote"    column="form_note"    />
        <result property="formNote1"    column="form_note1"    />
        <result property="frequency"    column="frequency"    />
        <result property="formType"    column="form_type"    />
        <result property="answerId"    column="answer_id"    />
        <result property="fcDate"    column="fc_date"    />
        <result property="formValue"    column="form_value"    />
        <result property="deptName"    column="dept_name"    />
        <result property="deptCode"    column="dept_code"    />
        <result property="reason"    column="reason"    />
        <result property="measure"    column="measure"    />
    </resultMap>

    <sql id="selectStaticAnswerVo">
        select id, dimensionality_id, dimensionality_path, dimensionality_name0, dimensionality_name1, dimensionality_name2, dimensionality_name3, dimensionality_name4, dimensionality_name5, form_id, form_question,formUnit, form_note,form_note1, frequency, form_type, answer_id, fc_date, form_value, dept_name, dept_code from static_answer
    </sql>

    <select id="selectStaticAnswerList" parameterType="StaticAnswer" resultMap="StaticAnswerResult">
        <include refid="selectStaticAnswerVo"/>
        <where>  
            <if test="dimensionalityId != null "> and dimensionality_id = #{dimensionalityId}</if>
            <if test="dimensionalityPath != null  and dimensionalityPath != ''"> and dimensionality_path = #{dimensionalityPath}</if>
            <if test="dimensionalityName0 != null  and dimensionalityName0 != ''"> and dimensionality_name0 = #{dimensionalityName0}</if>
            <if test="dimensionalityName1 != null  and dimensionalityName1 != ''"> and dimensionality_name1 = #{dimensionalityName1}</if>
            <if test="dimensionalityName2 != null  and dimensionalityName2 != ''"> and dimensionality_name2 = #{dimensionalityName2}</if>
            <if test="dimensionalityName3 != null  and dimensionalityName3 != ''"> and dimensionality_name3 = #{dimensionalityName3}</if>
            <if test="dimensionalityName4 != null  and dimensionalityName4 != ''"> and dimensionality_name4 = #{dimensionalityName4}</if>
            <if test="dimensionalityName5 != null  and dimensionalityName5 != ''"> and dimensionality_name5 = #{dimensionalityName5}</if>
            <if test="formId != null "> and form_id = #{formId}</if>
            <if test="formQuestion != null  and formQuestion != ''"> and form_question = #{formQuestion}</if>
            <if test="formUnit != null  and formUnit != ''"> and form_unit = #{formUnit}</if>
            <if test="formNote != null  and formNote != ''"> and form_note = #{formNote}</if>
            <if test="formNote1 != null  and formNote1 != ''"> and form_note1 = #{formNote1}</if>
            <if test="frequency != null  and frequency != ''"> and frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and form_type = #{formType}</if>
            <if test="answerId != null "> and answer_id = #{answerId}</if>
            <if test="fcDate != null  and fcDate != ''"> and fc_date = #{fcDate}</if>
            <if test="formValue != null  and formValue != ''"> and form_value = #{formValue}</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="deptCode != null  and deptCode != ''"> and dept_code = #{deptCode}</if>
        </where>
    </select>
    
    <select id="selectStaticAnswerById" parameterType="Long" resultMap="StaticAnswerResult">
        <include refid="selectStaticAnswerVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertStaticAnswer" parameterType="StaticAnswer">
        delete from static_answer
        where form_id = #{formId} and fc_date=#{fcDate};
        insert into static_answer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="dimensionalityId != null">dimensionality_id,</if>
            <if test="dimensionalityPath != null">dimensionality_path,</if>
            <if test="dimensionalityName0 != null">dimensionality_name0,</if>
            <if test="dimensionalityName1 != null">dimensionality_name1,</if>
            <if test="dimensionalityName2 != null">dimensionality_name2,</if>
            <if test="dimensionalityName3 != null">dimensionality_name3,</if>
            <if test="dimensionalityName4 != null">dimensionality_name4,</if>
            <if test="dimensionalityName5 != null">dimensionality_name5,</if>
            <if test="formId != null">form_id,</if>
            <if test="formQuestion != null">form_question,</if>
            <if test="formNote != null">form_note,</if>
            <if test="formNote1 != null">form_note1,</if>
            <if test="formUnit != null">form_unit,</if>
            <if test="frequency != null">frequency,</if>
            <if test="formType != null">form_type,</if>
            <if test="answerId != null">answer_id,</if>
            <if test="fcDate != null">fc_date,</if>
            <if test="formValue != null">form_value,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="deptCode != null">dept_code,</if>
            <if test="reason != null">reason,</if>
            <if test="measure != null">measure,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="dimensionalityId != null">#{dimensionalityId},</if>
            <if test="dimensionalityPath != null">#{dimensionalityPath},</if>
            <if test="dimensionalityName0 != null">#{dimensionalityName0},</if>
            <if test="dimensionalityName1 != null">#{dimensionalityName1},</if>
            <if test="dimensionalityName2 != null">#{dimensionalityName2},</if>
            <if test="dimensionalityName3 != null">#{dimensionalityName3},</if>
            <if test="dimensionalityName4 != null">#{dimensionalityName4},</if>
            <if test="dimensionalityName5 != null">#{dimensionalityName5},</if>
            <if test="formId != null">#{formId},</if>
            <if test="formQuestion != null">#{formQuestion},</if>
            <if test="formNote != null">#{formNote},</if>
            <if test="formNote1 != null">#{formNote1},</if>
            <if test="formUnit != null">#{formUnit},</if>
            <if test="frequency != null">#{frequency},</if>
            <if test="formType != null">#{formType},</if>
            <if test="answerId != null">#{answerId},</if>
            <if test="fcDate != null">#{fcDate},</if>
            <if test="formValue != null">#{formValue},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="deptCode != null">#{deptCode},</if>
            <if test="reason != null">#{reason},</if>
            <if test="measure != null">#{measure},</if>
         </trim>
    </insert>

    <update id="updateStaticAnswer" parameterType="StaticAnswer">
        update static_answer
        <trim prefix="SET" suffixOverrides=",">
            <if test="dimensionalityId != null">dimensionality_id = #{dimensionalityId},</if>
            <if test="dimensionalityPath != null">dimensionality_path = #{dimensionalityPath},</if>
            <if test="dimensionalityName0 != null">dimensionality_name0 = #{dimensionalityName0},</if>
            <if test="dimensionalityName1 != null">dimensionality_name1 = #{dimensionalityName1},</if>
            <if test="dimensionalityName2 != null">dimensionality_name2 = #{dimensionalityName2},</if>
            <if test="dimensionalityName3 != null">dimensionality_name3 = #{dimensionalityName3},</if>
            <if test="dimensionalityName4 != null">dimensionality_name4 = #{dimensionalityName4},</if>
            <if test="dimensionalityName5 != null">dimensionality_name5 = #{dimensionalityName5},</if>
            <if test="formId != null">form_id = #{formId},</if>
            <if test="formQuestion != null">form_question = #{formQuestion},</if>
            <if test="formNote != null">form_note = #{formNote},</if>
            <if test="formNote1 != null">form_note1 = #{formNote1},</if>
            <if test="formUnit != null">form_unit = #{formUnit},</if>
            <if test="frequency != null">frequency = #{frequency},</if>
            <if test="formType != null">form_type = #{formType},</if>
            <if test="answerId != null">answer_id = #{answerId},</if>
            <if test="fcDate != null">fc_date = #{fcDate},</if>
            <if test="formValue != null">form_value = #{formValue},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="deptCode != null">dept_code = #{deptCode},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="measure != null">measure = #{measure},</if>
        </trim>
        where form_id = #{formId}
        <if test="fcDate != null  and fcDate != ''"> and fc_date = #{fcDate}</if>
    </update>

    <delete id="deleteStaticAnswerById" parameterType="Long">
        delete from static_answer where id = #{id}
    </delete>

    <delete id="deleteStaticAnswerByIds" parameterType="String">
        delete from static_answer where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>