package com.ruoyi.web.controller.xctg;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.xml.crypto.Data;

import com.ruoyi.app.v1.controller.AppBaseV1Controller;
import com.ruoyi.app.v1.domain.*;
import com.ruoyi.app.v1.service.*;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysUserService;
import org.apache.commons.io.IOUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 绩效考核-自评信息Controller
 * 
 * <AUTHOR>
 * @date 2024-03-26
 */
@RestController
@RequestMapping("/web/selfAssess/info")
public class WebHrSelfAssessInfoController extends BaseController

{
    @Autowired
    private IHrSelfAssessInfoService hrSelfAssessInfoService;
    @Autowired
    private IHrSelfAssessTargetService hrSelfAssessTargetService;
    @Autowired
    private IHrSelfAssessUserService hrSelfAssessUserService;
    @Autowired
    private IHrLateralAssessDeptService hrLateralAssessDeptService;
    @Autowired
    private ISysUserService sysUserService;


    /**
     * 查询绩效考核-自评信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(HrSelfAssessInfo hrSelfAssessInfo)
    {
        startPage();
        List<HrSelfAssessInfo> list = hrSelfAssessInfoService.findList(hrSelfAssessInfo);
        return getDataTable(list);
    }

    @GetMapping("/listAll")
    public AjaxResult listAll(HrSelfAssessInfo hrSelfAssessInfo)
    {
        List<HrSelfAssessInfo> list = hrSelfAssessInfoService.findList(hrSelfAssessInfo);
        return success(list);
    }

    /**
     *
     * 获取审核列表
     * @param hrSelfAssessInfo
     * @return
     */
    @GetMapping("/listToCheck")
    public TableDataInfo listToCheck(HrSelfAssessInfo hrSelfAssessInfo)
    {
        String workNo = SecurityUtils.getUsername();
        hrSelfAssessInfo.setWorkNo(workNo);
        startPage();
        List<HrSelfAssessInfo> list = hrSelfAssessInfoService.listToCheck(hrSelfAssessInfo);
        return getDataTable(list);
    }

    /**
     *
     * 获取审核列表
     * @param hrSelfAssessInfo
     * @return
     */
    @GetMapping("/listLeaderToCheck")
    public TableDataInfo listLeaderToCheck(HrSelfAssessInfo hrSelfAssessInfo)
    {
        String workNo = SecurityUtils.getUsername();
        hrSelfAssessInfo.setLeaderNo(workNo);
        startPage();
        List<HrSelfAssessInfo> list = hrSelfAssessInfoService.listLeaderToCheck(hrSelfAssessInfo);
        return getDataTable(list);
    }

    /**
     *
     * 获取审核列表
     * @param hrSelfAssessCheckRecord
     * @return
     */
    @GetMapping("/listChecked")
    public TableDataInfo listChecked(HrSelfAssessCheckRecord hrSelfAssessCheckRecord)
    {
        String workNo = SecurityUtils.getUsername();
        hrSelfAssessCheckRecord.setCreateBy(workNo);
        startPage();
        List<HrSelfAssessCheckRecord> list = hrSelfAssessInfoService.listChecked(hrSelfAssessCheckRecord);
        return getDataTable(list);
    }

    @GetMapping("/batchWithBenefitByIds/{ids}")
    public AjaxResult batchWithBenefitByIds(@PathVariable Long[] ids)
    {
        return success(hrSelfAssessInfoService.batchWithBenefitByIds(ids));
    }

    /**
     * 导出绩效考核-自评信息列表
     */
    @Log(title = "绩效考核-自评信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(HttpServletResponse response, @RequestBody HrSelfAssessInfo hrSelfAssessInfo)
    {
        if(StringUtils.isNull(hrSelfAssessInfo.getAssessDate()) ) error("缺少必要参数");
        List<HrSelfAssessInfo> list = hrSelfAssessInfoService.findList(hrSelfAssessInfo);
        ExcelUtil<HrSelfAssessInfo> util = new ExcelUtil<HrSelfAssessInfo>(HrSelfAssessInfo.class);
        return util.exportExcel(list, "月度绩效考核汇总");
    }

    /**
     * 导出绩效考核-自评信息列表
     */
    @Log(title = "绩效考核-自评信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportDetail")
    public AjaxResult exportDetail(HttpServletResponse response,@RequestBody HrSelfAssessInfo hrSelfAssessInfo)
    {
        if(StringUtils.isNull(hrSelfAssessInfo.getId()) ) return error("缺少必要参数");
        HrSelfAssessInfo info = hrSelfAssessInfoService.get(hrSelfAssessInfo.getId());
        return success(hrSelfAssessInfoService.exportDetail(info));
    }


    /**
     * 导出绩效考核-自评信息列表
     */
    @Log(title = "绩效考核-自评信息", businessType = BusinessType.EXPORT)
    @GetMapping("/batchExportDetail")
    public void batchExportDetail(HttpServletResponse response, HrSelfAssessInfo hrSelfAssessInfo) throws IOException
    {
        if(StringUtils.isNull(hrSelfAssessInfo.getAssessDate()) ) error("缺少必要参数");
        Date assessDate = hrSelfAssessInfo.getAssessDate();
        String dateStr = new SimpleDateFormat("yyyy年MM月").format(assessDate);
        byte[] data = hrSelfAssessInfoService.downloadDetailZip(hrSelfAssessInfo);
        genZip(response,data,dateStr);
    }

    /**
     * 导出绩效考核-自评信息列表
     */
    @Log(title = "绩效考核-自评信息", businessType = BusinessType.EXPORT)
    @PostMapping("/importFinalScore")
    public AjaxResult importFinalScore(MultipartFile file) throws Exception
    {
        ExcelUtil<HrSelfAssessInfo> util = new ExcelUtil<>(HrSelfAssessInfo.class);
        List<HrSelfAssessInfo> importList = util.importExcel(file.getInputStream());
        return success(hrSelfAssessInfoService.importFinalScore(importList));
    }

    /**
     * 导出技术序列业绩汇总表（按条线领导分Sheet）
     */
    @Log(title = "导出技术序列业绩汇总表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportTechnicalPerformanceSummary")
    public AjaxResult exportTechnicalPerformanceSummary(@RequestBody HrSelfAssessInfo hrSelfAssessInfo)
    {
        if(StringUtils.isNull(hrSelfAssessInfo.getAssessDate())) {
            return error("缺少必要参数：考核年月");
        }
        
        try {
            String filename = hrSelfAssessInfoService.exportTechnicalPerformanceSummary(hrSelfAssessInfo);
            return success(filename);
        } catch (Exception e) {
            logger.error("导出技术序列业绩汇总表失败", e);
            return error("导出失败：" + e.getMessage());
        }
    }

    /**
     * 导出技术序列业绩汇总表（按条线领导分Sheet）
     */
    @Log(title = "导出行政序列业绩汇总表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAdministrativePerformanceSummary")
    public AjaxResult exportAdministrativePerformanceSummary(@RequestBody HrSelfAssessInfo hrSelfAssessInfo)
    {
        if(StringUtils.isNull(hrSelfAssessInfo.getAssessDate())) {
            return error("缺少必要参数：考核年月");
        }

        try {
            String filename = hrSelfAssessInfoService.exportAdministrativePerformanceSummary(hrSelfAssessInfo);
            return success(filename);
        } catch (Exception e) {
            logger.error("导出技术序列业绩汇总表失败", e);
            return error("导出失败：" + e.getMessage());
        }
    }

    /**
     * 获取绩效考核-自评信息详细信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo(String id)
    {
        if(StringUtils.isBlank(id) ) error("缺少必要参数id");
        return success(hrSelfAssessInfoService.get(id));
    }

    /**
     * 获取绩效考核-自评信息详细信息
     */
    @GetMapping("/getInfoByDate")
    public AjaxResult getInfoByDate(HrSelfAssessInfo hrSelfAssessInfo)
    {
        if(StringUtils.isNull(hrSelfAssessInfo.getAssessDate()) ) return error("缺少必要参数:考核年月");
        if(StringUtils.isNull(hrSelfAssessInfo.getDeptId()) ) return error("缺少必要参数:部门");
        HrSelfAssessInfo info = hrSelfAssessInfoService.getByUserInfo(hrSelfAssessInfo);
        if(StringUtils.isNotNull(info)){
            return success(info);
        }else{
            // 没有当月信息从获取指标配置信息
            HrSelfAssessTarget hrSelfAssessTarget = new HrSelfAssessTarget();
            hrSelfAssessTarget.setUserId(hrSelfAssessInfo.getUserId());
            List<HrSelfAssessTarget> list = hrSelfAssessTargetService.findList(hrSelfAssessTarget);
            return success(list);
        }

    }

    /**
     * 获取绩效考核-部门被考核信息
     */
    @GetMapping("/listBeAssessed")
    public AjaxResult listBeAssessed(HrSelfAssessInfo hrSelfAssessInfo)
    {
        if(StringUtils.isNull(hrSelfAssessInfo.getAssessDate()) ) return error("缺少必要参数:考核年月");
        if(StringUtils.isNull(hrSelfAssessInfo.getDeptId()) ) return error("缺少必要参数:部门");
        return success(hrSelfAssessInfoService.listBeAssessed(hrSelfAssessInfo));
    }

    /**
     * 新增绩效考核-自评信息
     */
    @Log(title = "绩效考核-自评信息", businessType = BusinessType.INSERT)
    @PostMapping("/insert")
    public AjaxResult insert(@RequestBody HrSelfAssessInfo hrSelfAssessInfo)
    {
        if(StringUtils.isNull(hrSelfAssessInfo.getUserId()) ) return error("缺少必要参数");
        HrSelfAssessUser user = hrSelfAssessUserService.get(hrSelfAssessInfo.getUserId());
        if(StringUtils.isNotNull(user)){
            return toAjax(hrSelfAssessInfoService.insert(hrSelfAssessInfo,user));
        }else{
            return error("没有自评权限,请联系管理员");
        }
    }

    /**
     * 新增绩效考核-自评信息-保存
     */
    @Log(title = "绩效考核-自评信息", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult save(@RequestBody HrSelfAssessInfo hrSelfAssessInfo)
    {
        if(StringUtils.isNull(hrSelfAssessInfo.getUserId()) ) return error("缺少必要参数");
        HrSelfAssessUser user = hrSelfAssessUserService.get(hrSelfAssessInfo.getUserId());
        if(StringUtils.isNotNull(user)){
            return toAjax(hrSelfAssessInfoService.save(hrSelfAssessInfo));
        }else{
            return error("没有自评权限,请联系管理员");
        }
    }

    /**
     * 新增绩效考核-自评信息-保存
     */
    @Log(title = "绩效考核-自评信息", businessType = BusinessType.INSERT)
    @PostMapping("/submit")
    public AjaxResult submit(@RequestBody HrSelfAssessInfo hrSelfAssessInfo)
    {
        if(StringUtils.isNull(hrSelfAssessInfo.getUserId()) ) return error("缺少必要参数");
        HrSelfAssessUser user = hrSelfAssessUserService.get(hrSelfAssessInfo.getUserId());
        if(StringUtils.isNotNull(user)){
            return toAjax(hrSelfAssessInfoService.submit(hrSelfAssessInfo,user));
        }else{
            return error("没有自评权限,请联系管理员");
        }
    }

    /**
     * 修改绩效考核-自评信息
     */
    @Log(title = "绩效考核-自评信息", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@RequestBody HrSelfAssessInfo hrSelfAssessInfo)
    {
        return toAjax(hrSelfAssessInfoService.update(hrSelfAssessInfo));
    }

    /**
     * 修改绩效考核-自评信息
     */
    @Log(title = "绩效考核-自评信息", businessType = BusinessType.UPDATE)
    @PostMapping("/check")
    public AjaxResult check(@RequestBody HrSelfAssessInfo hrSelfAssessInfo)
    {
        if(StringUtils.isNull(hrSelfAssessInfo.getId()) ) return error("缺少必要参数");
        SysUser user = SecurityUtils.getLoginUser().getUser();
        HrSelfAssessUser hrSelfAssessUser = new HrSelfAssessUser();
        hrSelfAssessUser.setWorkNo(user.getUserName());
        hrSelfAssessUser.setName(user.getNickName());
        return toAjax(hrSelfAssessInfoService.check(hrSelfAssessInfo,hrSelfAssessUser));
    }

    /**
     * 退回绩效考核-自评信息
     */
    @Log(title = "绩效考核-自评信息", businessType = BusinessType.UPDATE)
    @PostMapping("/rejectInfo")
    public AjaxResult rejectInfo(@RequestBody HrSelfAssessInfo hrSelfAssessInfo)
    {
        if(StringUtils.isNull(hrSelfAssessInfo.getId()) ) return error("缺少必要参数");
        if(StringUtils.isNull(hrSelfAssessInfo.getStatus()) ) return error("缺少必要参数：退回状态");

        // 添加调试日志
        System.out.println("Controller接收到的参数 - ID: " + hrSelfAssessInfo.getId() + ", Status: " + hrSelfAssessInfo.getStatus());

        HrSelfAssessInfo rejectInfo = new HrSelfAssessInfo();
        rejectInfo.setId(hrSelfAssessInfo.getId());
        rejectInfo.setStatus(hrSelfAssessInfo.getStatus()); // 设置退回目标状态
        rejectInfo.setUpdateBy(SecurityUtils.getUsername());
        rejectInfo.setRejectReason(hrSelfAssessInfo.getRejectReason());

        System.out.println("传递给Service的参数 - ID: " + rejectInfo.getId() + ", Status: " + rejectInfo.getStatus());

        return toAjax(hrSelfAssessInfoService.reject(rejectInfo));
    }

    /**
     * 删除绩效考核-自评信息
     */
    @Log(title = "绩效考核-自评信息", businessType = BusinessType.DELETE)
	@PutMapping("/delete")
    public AjaxResult PutMapping(@RequestBody HrSelfAssessInfo hrSelfAssessInfo)
    {
        return toAjax(hrSelfAssessInfoService.delete(hrSelfAssessInfo));
    }

    /**
     * 生成zip文件
     */
    private void genZip(HttpServletResponse response, byte[] data,String assessDate) throws IOException
    {
        String fileName = assessDate + "绩效考核汇总.zip";
        fileName = URLEncoder.encode(fileName,"UTF-8");
        response.reset();
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
        response.addHeader("Content-Length", "" + data.length);
        response.setContentType("application/octet-stream; charset=UTF-8");
        IOUtils.write(data, response.getOutputStream());
    }

    /**
     * 批量快速评分
     */
    @PostMapping("/batchQuickScore")
    public AjaxResult batchQuickScore(@RequestBody List<HrSelfAssessInfo> infoList) {
        try {
            SysUser user = sysUserService.selectUserByUserName(SecurityUtils.getUsername());
            return toAjax(hrSelfAssessInfoService.batchQuickScore(infoList, user));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }
}
