<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.v1.mapper.HrSelfAssessInfoMapper">

    <resultMap type="HrSelfAssessInfo" id="HrSelfAssessInfoResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="type"    column="type"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="workNo"    column="work_no"    />
        <result property="name"    column="name"    />
        <result property="job"    column="job"    />
        <result property="sign"    column="sign"    />
        <result property="assessDate"    column="assess_date"    />
        <result property="selfScore"    column="self_score"    />
        <result property="deptScore"    column="dept_score"    />
        <result property="deptUserNo"    column="dept_user_no"    />
        <result property="deptUserName"    column="dept_user_name"    />
        <result property="deptScoreReason"    column="dept_score_reason"    />
        <result property="businessDeptId"    column="business_dept_id"    />
        <result property="businessDeptName"    column="business_dept_name"    />
        <result property="businessScore"    column="business_score"    />
        <result property="businessUserNo"    column="business_user_no"    />
        <result property="businessUserName"    column="business_user_name"    />
        <result property="businessScoreReason"    column="business_score_reason"    />
        <result property="organizationScore"    column="organization_score"    />
        <result property="organizationUserNo"    column="organization_user_no"    />
        <result property="organizationUserName"    column="organization_user_name"    />
        <result property="organizationScoreReason"    column="organization_score_reason"    />
        <result property="rejectReason"    column="reject_reason"    />
        <result property="finalScore"    column="final_score"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="benefitLinkFlag"    column="benefit_link_flag"    />
        <result property="averageLinkFlag"    column="average_link_flag"    />
        <result property="postType"    column="post_type"    />
    </resultMap>

    <resultMap id="HrSelfAssessLeaderCheckResult" type="HrSelfAssessLeaderCheck">
        <result property="id"    column="id"    />
        <result property="infoId"    column="info_id"    />
        <result property="workNo"    column="work_no"    />
        <result property="leaderScore"    column="leader_score"    />
        <result property="leaderName"    column="leader_name"    />
        <result property="leaderReview"    column="leader_review"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectHrSelfAssessInfoVo">
        select id,user_id, content, status, type, business_dept_id, business_dept_name, dept_id, dept_name, work_no, name, job, sign, assess_date, self_score, dept_score, dept_user_no, dept_user_name, dept_score_reason, business_score, business_user_no, business_user_name, business_score_reason, organization_score, organization_user_no, organization_user_name, organization_score_reason, reject_reason, final_score, create_by, create_time, update_by, update_time, benefit_link_flag, average_link_flag, post_type  from hr_self_assess_info
    </sql>

    <select id="findList" parameterType="HrSelfAssessInfo" resultMap="HrSelfAssessInfoResult">
        <include refid="selectHrSelfAssessInfoVo"/>
        <where>
            del_flag = '0'
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="workNo != null  and workNo != ''"> and work_no = #{workNo}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="job != null  and job != ''"> and job = #{job}</if>
            <if test="assessDate != null"> and assess_date = #{assessDate}</if>
            <if test="postType != null  and postType != ''"> and post_type = #{postType}</if>
        </where>
    </select>

    <select id="get" parameterType="String" resultMap="HrSelfAssessInfoResult">
        <include refid="selectHrSelfAssessInfoVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <select id="getByUserInfo" parameterType="HrSelfAssessInfo" resultMap="HrSelfAssessInfoResult">
        <include refid="selectHrSelfAssessInfoVo"/>
        where work_no = #{workNo} and assess_date = #{assessDate} and dept_id = #{deptId} and del_flag = '0'
    </select>

    <select id="listToCheck" parameterType="HrSelfAssessInfo" resultMap="HrSelfAssessInfoResult">
        select id, name, dept_name, work_no,job, assess_date, type, status, self_score, dept_score, dept_user_no, dept_user_name, dept_score_reason, business_score, business_user_no, business_user_name, business_score_reason, organization_score, organization_user_no, organization_user_name, benefit_link_flag, average_link_flag, post_type
        from hr_self_assess_info
        where assess_date = #{assessDate} and del_flag = '0'
        <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
        <if test="deptId != null ">
            and (
                (dept_id in(select d.dept_id from hr_self_assess_user u
                    left join hr_self_assess_user_dept d on u.id = d.user_id
                    where u.work_no = #{workNo} and d.dept_id = #{deptId} and u.assess_role = '1')
                    and status = '1'
                )
                or (status = '2' and business_dept_id in (select d.dept_id from hr_self_assess_user u
                    left join hr_self_assess_user_dept d on u.id = d.user_id
                    where u.work_no = #{workNo} and d.dept_id = #{deptId})
                )
            )
        </if>
        <if test="deptId == null ">
            and (
                (status = '1' and dept_id in (select d.dept_id from hr_self_assess_user u
                    left join hr_self_assess_user_dept d on u.id = d.user_id
                    where u.work_no = #{workNo} and u.assess_role = '1')
                )
                or (status = '2' and business_dept_id in (select d.dept_id from hr_self_assess_user u
                    left join hr_self_assess_user_dept d on u.id = d.user_id
                    where u.work_no = #{workNo})
                )
            )
        </if>
    </select>

<!--    or (dept_id in (select d.dept_id from hr_self_assess_user u-->
<!--    left join hr_self_assess_user_dept d on u.id = d.user_id-->
<!--    where u.work_no = #{workNo} and d.dept_id = #{deptId} and u.assess_role = '2')-->
<!--    and status = '3')-->

<!--    or (status = '3' and dept_id in (select d.dept_id from hr_self_assess_user u-->
<!--    left join hr_self_assess_user_dept d on u.id = d.user_id-->
<!--    where u.work_no = #{workNo} and u.assess_role = '2')-->
<!--    )-->

    <select id="listLeaderToCheck" parameterType="HrSelfAssessInfo" resultMap="HrSelfAssessInfoResult">
        select i.id,i.name,i.dept_name,i.work_no,i.job,i.assess_date,i.type,i.status,i.self_score
        from hr_self_assess_info i
        left join hr_self_assess_leader_check c on c.info_id = i.id
        where i.assess_date = #{assessDate} and i.del_flag = '0' and i.status = '4'
        and i.user_id in <foreach item="item" index="index" collection="userIds" separator="," open="(" close=")">#{item}</foreach>
        and i.id not in (select info_id from hr_self_assess_leader_check where work_no = #{leaderNo})
        <if test="name != null  and name != ''"> and i.name like concat('%', #{name}, '%')</if>
        <if test="deptId != null "> and i.dept_id = #{deptId} </if>
    </select>



    <insert id="insert" parameterType="HrSelfAssessInfo" useGeneratedKeys="true" keyProperty="id">
        insert into hr_self_assess_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="content != null">content,</if>
            <if test="status != null">status,</if>
            <if test="type != null">type,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="workNo != null">work_no,</if>
            <if test="name != null">name,</if>
            <if test="job != null">job,</if>
            <if test="sign != null">sign,</if>
            <if test="assessDate != null">assess_date,</if>
            <if test="selfScore != null">self_score,</if>
            <if test="deptScore != null">dept_score,</if>
            <if test="deptUserNo != null">dept_user_no,</if>
            <if test="deptUserName != null">dept_user_name,</if>
            <if test="businessDeptId != null">business_dept_id,</if>
            <if test="businessDeptName != null">business_dept_name,</if>
            <if test="businessScore != null">business_score,</if>
            <if test="businessUserNo != null">business_user_no,</if>
            <if test="businessUserName != null">business_user_name,</if>
            <if test="organizationScore != null">organization_score,</if>
            <if test="organizationUserNo != null">organization_user_no,</if>
            <if test="organizationUserName != null">organization_user_name,</if>
            <if test="organizationScoreReason != null">organization_score_reason,</if>
            <if test="rejectReason != null">reject_reason,</if>
            <if test="finalScore != null">final_score,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="benefitLinkFlag != null">benefit_link_flag,</if>
            <if test="averageLinkFlag != null">average_link_flag,</if>
            <if test="postType != null">post_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="content != null">#{content},</if>
            <if test="status != null">#{status},</if>
            <if test="type != null">#{type},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="workNo != null">#{workNo},</if>
            <if test="name != null">#{name},</if>
            <if test="job != null">#{job},</if>
            <if test="sign != null">#{sign},</if>
            <if test="assessDate != null">#{assessDate},</if>
            <if test="selfScore != null">#{selfScore},</if>
            <if test="deptScore != null">#{deptScore},</if>
            <if test="deptUserNo != null">#{deptUserNo},</if>
            <if test="deptUserName != null">#{deptUserName},</if>
            <if test="businessDeptId != null">#{businessDeptId},</if>
            <if test="businessDeptName != null">#{businessDeptName},</if>
            <if test="businessScore != null">#{businessScore},</if>
            <if test="businessUserNo != null">#{businessUserNo},</if>
            <if test="businessUserName != null">#{businessUserName},</if>
            <if test="organizationScore != null">#{organizationScore},</if>
            <if test="organizationUserNo != null">#{organizationUserNo},</if>
            <if test="organizationUserName != null">#{organizationUserName},</if>
            <if test="organizationScoreReason != null">#{organizationScoreReason},</if>
            <if test="rejectReason != null">#{rejectReason},</if>
            <if test="finalScore != null">#{finalScore},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="benefitLinkFlag != null">#{benefitLinkFlag},</if>
            <if test="averageLinkFlag != null">#{averageLinkFlag},</if>
            <if test="postType != null">#{postType},</if>
         </trim>
    </insert>

    <update id="update" parameterType="HrSelfAssessInfo">
        update hr_self_assess_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="content != null">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="type != null">type = #{type},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="workNo != null">work_no = #{workNo},</if>
            <if test="name != null">name = #{name},</if>
            <if test="job != null">job = #{job},</if>
            <if test="sign != null">sign = #{sign},</if>
            <if test="assessDate != null">assess_date = #{assessDate},</if>
            <if test="selfScore != null">self_score = #{selfScore},</if>
            <if test="deptScore != null">dept_score = #{deptScore},</if>
            <if test="deptUserNo != null">dept_user_no = #{deptUserNo},</if>
            <if test="deptUserName != null">dept_user_name = #{deptUserName},</if>
            <if test="deptScoreReason != null">dept_score_reason = #{deptScoreReason},</if>
            <if test="businessDeptId != null">business_dept_id = #{businessDeptId},</if>
            <if test="businessDeptName != null">business_dept_name = #{businessDeptName},</if>
            <if test="businessScore != null">business_score = #{businessScore},</if>
            <if test="businessUserNo != null">business_user_no = #{businessUserNo},</if>
            <if test="businessUserName != null">business_user_name = #{businessUserName},</if>
            <if test="businessScoreReason != null">business_score_reason = #{businessScoreReason},</if>
            <if test="organizationScore != null">organization_score = #{organizationScore},</if>
            <if test="organizationUserNo != null">organization_user_no = #{organizationUserNo},</if>
            <if test="organizationUserName != null">organization_user_name = #{organizationUserName},</if>
            <if test="organizationScoreReason != null">organization_score_reason = #{organizationScoreReason},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="finalScore != null">final_score = #{finalScore},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="benefitLinkFlag != null">benefit_link_flag = #{benefitLinkFlag},</if>
            <if test="averageLinkFlag != null">average_link_flag = #{averageLinkFlag},</if>
            <if test="postType != null">post_type = #{postType},</if>
            update_time = sysdate(),
        </trim>
        where id = #{id}
    </update>

    <update id="reject" parameterType="HrSelfAssessInfo">
        update hr_self_assess_info
        set status = #{status},
            <!-- 清空部门领导评分及以后的所有评分数据 -->
            dept_score = null,
            dept_user_no = null,
            dept_user_name = null,
            dept_score_reason = null,
            business_score = null,
            business_user_no = null,
            business_user_name = null,
            business_score_reason = null,
            organization_score = null,
            organization_user_no = null,
            organization_user_name = null,
            organization_score_reason = null,
            final_score = null,
            update_time = sysdate()
            <if test="rejectReason != null">, reject_reason = #{rejectReason}</if>
            <if test="updateBy != null">, update_by = #{updateBy}</if>
        where id = #{id}
    </update>

    <update id="setFinalScoreByWorkNo" parameterType="HrSelfAssessInfo">
        update hr_self_assess_info
        set final_score = #{finalScore}
        where work_no = #{workNo} and del_flag = '0'
    </update>

    <update id="delete" parameterType="HrSelfAssessInfo">
        update hr_self_assess_info
        set
            del_flag = #{delFlag},
            delete_by = #{deleteBy},
            delete_time = #{deleteTime}
        where id = #{id}
    </update>



    <insert id="batch">
        insert into hr_self_assess_info( id, content, status, type, business_dept_id, business_dept_name, dept_id, dept_name, work_no, name, job, assess_date, self_score, dept_score, dept_user_no, dept_user_name, business_score, business_user_no, business_user_name, final_score, create_by, create_time, update_by, update_time) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.content}, #{item.status}, #{item.type}, #{businessDeptId}, #{businessDeptName} #{item.deptId}, #{item.deptName}, #{item.workNo}, #{item.name}, #{item.job}, #{item.assessDate}, #{item.selfScore}, #{item.deptScore}, #{item.deptUserNo}, #{item.deptUserName}, #{item.businessScore}, #{item.businessUserNo}, #{item.businessUserName}, #{item.finalScore}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>


    <select id="selectHrSelfAssessLeaderCheckByInfoId" parameterType="String" resultMap="HrSelfAssessLeaderCheckResult">
        select id, info_id, work_no, leader_name, leader_score, leader_review, create_time from hr_self_assess_leader_check
        where info_id = #{infoId}
    </select>

    <insert id="batchHrSelfAssessLeaderCheck">
        insert into hr_self_assess_leader_check( info_id, work_no, leader_name, leader_score, leader_review, create_time ) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.infoId}, #{item.workNo}, #{item.leaderName}, #{item.leaderScore}, #{item.leaderReview}, NOW() )
        </foreach>
    </insert>

    <insert id="insertHrSelfAssessLeaderCheck" parameterType="HrSelfAssessLeaderCheck">
        insert into hr_self_assess_leader_check
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="infoId != null">info_id,</if>
            <if test="workNo != null">work_no,</if>
            <if test="leaderName != null">leader_name,</if>
            <if test="leaderScore != null">leader_score,</if>
            <if test="leaderReview != null">leader_review,</if>
            create_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="infoId != null">#{infoId},</if>
            <if test="workNo != null">#{workNo},</if>
            <if test="leaderName != null">#{leaderName},</if>
            <if test="leaderScore != null">#{leaderScore},</if>
            <if test="leaderReview != null">#{leaderReview},</if>
            NOW(),
        </trim>
    </insert>

    <delete id="deleteHrSelfAssessLeaderCheck">
        delete from hr_self_assess_leader_check where info_id = #{id}
    </delete>

    <!-- 批量查询评分信息 -->
    <select id="selectBatchByIds" resultMap="HrSelfAssessInfoResult">
        <include refid="selectHrSelfAssessInfoVo"/>
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 批量更新快速评分 -->
    <update id="batchUpdateQuickScore">
        UPDATE hr_self_assess_info
        <trim prefix="SET" suffixOverrides=",">
            <!-- dept_score -->
            <trim prefix="dept_score = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} AND status = '1' THEN #{item.quickScore}
                </foreach>
                ELSE dept_score
            </trim>
            <trim prefix="dept_score_reason = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} AND status = '1' THEN #{item.quickReason}
                </foreach>
                ELSE dept_score_reason
            </trim>
            <!-- dept_user_no -->
            <trim prefix="dept_user_no = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} AND status = '1' THEN #{item.deptUserNo}
                </foreach>
                ELSE dept_user_no
            </trim>

            <!-- dept_user_name -->
            <trim prefix="dept_user_name = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} AND status = '1' THEN #{item.deptUserName}
                </foreach>
                ELSE dept_user_name
            </trim>

            <!-- business_score -->
            <trim prefix="business_score = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} AND status = '2' THEN #{item.quickScore}
                </foreach>
                ELSE business_score
            </trim>
            <trim prefix="business_score_reason = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} AND status = '2' THEN #{item.quickReason}
                </foreach>
                ELSE business_score_reason
            </trim>

            <!-- business_user_no -->
            <trim prefix="business_user_no = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} AND status = '2' THEN #{item.businessUserNo}
                </foreach>
                ELSE business_user_no
            </trim>

            <!-- business_user_name -->
            <trim prefix="business_user_name = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} AND status = '2' THEN #{item.businessUserName}
                </foreach>
                ELSE business_user_name
            </trim>

            <!-- organization_score -->
            <trim prefix="organization_score = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} AND status = '3' THEN #{item.quickScore}
                </foreach>
                ELSE organization_score
            </trim>
            <trim prefix="organization_score_reason = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} AND status = '3' THEN #{item.quickReason}
                </foreach>
                ELSE organization_score_reason
            </trim>

            <!-- organization_user_no -->
            <trim prefix="organization_user_no = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} AND status = '3' THEN #{item.organizationUserNo}
                </foreach>
                ELSE organization_user_no
            </trim>

            <!-- organization_user_name -->
            <trim prefix="organization_user_name = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} AND status = '3' THEN #{item.organizationUserName}
                </foreach>
                ELSE organization_user_name
            </trim>

            <!-- status 状态升级 -->
            <trim prefix="status = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} THEN #{item.status}
                </foreach>
                ELSE status
            </trim>

            <!-- update_time -->
            <trim prefix="update_time = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} THEN SYSDATE()
                </foreach>
                ELSE update_time
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="batchByIds" parameterType="String" resultMap="HrSelfAssessInfoResult">
        select * from hr_self_assess_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 为 HrSelfAssessUser 添加 resultMap -->
    <resultMap type="HrSelfAssessUser" id="HrSelfAssessUserResult">
        <result property="id"    column="id"    />
        <result property="workNo"    column="work_no"    />
        <result property="name"    column="name"    />
        <result property="assessRole"    column="assess_role"    />
        <result property="postType"    column="post_type"    />
        <result property="job"    column="job"    />
        <result property="benefitLinkFlag"    column="benefit_link_flag"    />
        <result property="averageLinkFlag"    column="average_link_flag"    />
    </resultMap>

    <!-- 查询指定考核年月的所有条线领导列表（有技术序列干部的条线领导） -->
    <select id="selectLeadersWithTechnicalStaff" parameterType="HrSelfAssessInfo" resultMap="HrSelfAssessUserResult">
        SELECT DISTINCT 
            leader.id,
            leader.work_no,
            leader.name,
            leader.assess_role,
            leader.post_type,
            leader.job,
            leader.benefit_link_flag,
            leader.average_link_flag
        FROM hr_self_assess_user leader
        INNER JOIN hr_self_assess_user_leader ul ON leader.work_no = ul.work_no
        INNER JOIN hr_self_assess_user staff ON ul.user_id = staff.id
--         INNER JOIN hr_self_assess_user_dept sud ON staff.id = sud.user_id
        INNER JOIN hr_self_assess_info info ON info.user_id = staff.id
        WHERE leader.assess_role = '2'
            AND staff.post_type = '0'
            AND leader.del_flag = '0'
            AND staff.del_flag = '0'
            AND info.del_flag = '0'
            <if test="assessDate != null">
                AND info.assess_date = #{assessDate}
            </if>
        ORDER BY leader.name
    </select>

    <!-- 按条线领导查询技术序列干部的绩效考核信息 -->
    <select id="selectTechnicalStaffByLeader" parameterType="HrSelfAssessInfo" resultMap="HrSelfAssessInfoResult">
        SELECT 
            info.id,
            info.user_id,
            info.content,
            info.status,
            info.type,
            info.business_dept_id,
            info.business_dept_name,
            info.dept_id,
            info.dept_name,
            info.work_no,
            info.name,
            info.job,
            info.sign,
            info.assess_date,
            info.self_score,
            info.dept_score,
            info.dept_user_no,
            info.dept_user_name,
            info.dept_score_reason,
            info.business_score,
            info.business_user_no,
            info.business_user_name,
            info.business_score_reason,
            info.organization_score,
            info.organization_user_no,
            info.organization_user_name,
            info.organization_score_reason,
            info.reject_reason,
            info.final_score,
            info.create_by,
            info.create_time,
            info.update_by,
            info.update_time,
            info.benefit_link_flag,
            info.average_link_flag,
            info.post_type
        FROM hr_self_assess_info info
        INNER JOIN hr_self_assess_user staff ON info.user_id = staff.id
        INNER JOIN hr_self_assess_user_leader ul ON staff.id = ul.user_id
        WHERE staff.post_type = '0'
            AND staff.del_flag = '0'
            AND info.del_flag = '0'
            <if test="assessDate != null">
                AND info.assess_date = #{assessDate}
            </if>
            <if test="leaderNo != null and leaderNo != ''">
                AND ul.work_no = #{leaderNo}
            </if>
        ORDER BY info.dept_name, info.name
    </select>

    <!-- 查询指定考核年月的所有条线领导列表（有行政序列干部的条线领导） -->
    <select id="selectLeadersWithAdministrativeStaff" parameterType="HrSelfAssessInfo" resultMap="HrSelfAssessUserResult">
        SELECT DISTINCT 
            leader.id,
            leader.work_no,
            leader.name,
            leader.assess_role,
            leader.post_type,
            leader.job,
            leader.benefit_link_flag,
            leader.average_link_flag
        FROM hr_self_assess_user leader
        INNER JOIN hr_self_assess_user_leader ul ON leader.work_no = ul.work_no
        INNER JOIN hr_self_assess_user staff ON ul.user_id = staff.id
        INNER JOIN hr_self_assess_info info ON info.user_id = staff.id
        WHERE leader.assess_role = '2'
            AND staff.post_type = '1'
            AND leader.del_flag = '0'
            AND staff.del_flag = '0'
            AND info.del_flag = '0'
            <if test="assessDate != null">
                AND info.assess_date = #{assessDate}
            </if>
        ORDER BY leader.name
    </select>

    <!-- 按条线领导查询行政序列干部的绩效考核信息 -->
    <select id="selectAdministrativeStaffByLeader" parameterType="HrSelfAssessInfo" resultMap="HrSelfAssessInfoResult">
        SELECT 
            info.id,
            info.user_id,
            info.content,
            info.status,
            info.type,
            info.business_dept_id,
            info.business_dept_name,
            info.dept_id,
            info.dept_name,
            info.work_no,
            info.name,
            info.job,
            info.sign,
            info.assess_date,
            info.self_score,
            info.dept_score,
            info.dept_user_no,
            info.dept_user_name,
            info.dept_score_reason,
            info.business_score,
            info.business_user_no,
            info.business_user_name,
            info.business_score_reason,
            info.organization_score,
            info.organization_user_no,
            info.organization_user_name,
            info.organization_score_reason,
            info.reject_reason,
            info.final_score,
            info.create_by,
            info.create_time,
            info.update_by,
            info.update_time,
            info.benefit_link_flag,
            info.average_link_flag,
            info.post_type
        FROM hr_self_assess_info info
        INNER JOIN hr_self_assess_user staff ON info.user_id = staff.id
        INNER JOIN hr_self_assess_user_leader ul ON staff.id = ul.user_id
        WHERE staff.post_type = '1'
            AND staff.del_flag = '0'
            AND info.del_flag = '0'
            <if test="assessDate != null">
                AND info.assess_date = #{assessDate}
            </if>
            <if test="leaderNo != null and leaderNo != ''">
                AND ul.work_no = #{leaderNo}
            </if>
        ORDER BY info.dept_name, info.name
    </select>

</mapper>