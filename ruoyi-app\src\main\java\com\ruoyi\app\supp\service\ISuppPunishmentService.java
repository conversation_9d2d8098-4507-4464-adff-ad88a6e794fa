package com.ruoyi.app.supp.service;

import java.util.List;

import com.ruoyi.app.domain.MaterialInfo;
import com.ruoyi.app.supp.domain.ServiceProject;
import com.ruoyi.app.supp.domain.SuppInfo;
import com.ruoyi.app.supp.domain.SuppPunishment;
import com.ruoyi.app.supp.domain.SuppUser;

/**
 * 供应商处罚Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface ISuppPunishmentService
{
    /**
     * 查询供应商处罚
     *
     * @param id 供应商处罚ID
     * @return 供应商处罚
     */
    public SuppPunishment selectSuppPunishmentById(Long id);

    /**
     * 查询供应商处罚列表
     *
     * @param suppPunishment 供应商处罚
     * @return 供应商处罚集合
     */
    public List<SuppPunishment> selectSuppPunishmentList(SuppPunishment suppPunishment);

    /**
     * 新增供应商处罚
     *
     * @param suppPunishment 供应商处罚
     * @return 结果
     */
    public int insertSuppPunishment(SuppPunishment suppPunishment);

    /**
     * 修改供应商处罚
     *
     * @param suppPunishment 供应商处罚
     * @return 结果
     */
    public int updateSuppPunishment(SuppPunishment suppPunishment);

    /**
     * 批量删除供应商处罚
     *
     * @param ids 需要删除的供应商处罚ID
     * @return 结果
     */
    public int deleteSuppPunishmentByIds(Long[] ids);

    /**
     * 删除供应商处罚信息
     *
     * @param id 供应商处罚ID
     * @return 结果
     */
    public int deleteSuppPunishmentById(Long id);


    List<SuppInfo> selectSuppInfoList(SuppInfo suppInfo);

    List<MaterialInfo> selectMaterialInfoList(MaterialInfo materialInfo);

    public Object confirmSuppPunishmentByIds(Long[] ids);

    List<ServiceProject> selectServiceList(ServiceProject serviceProject);
    List<ServiceProject> selectProjectList(ServiceProject serviceProject);

    SuppUser selectUserGroupByUserName();

    List<String> selectDepNameList();
}
