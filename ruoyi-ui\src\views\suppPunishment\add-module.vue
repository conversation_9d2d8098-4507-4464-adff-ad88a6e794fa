<template>
  <!-- 添加或修改供应商处罚记录对话框 -->
  <el-dialog :title="title" :visible.sync="visible" :width="isFullscreen ? '100%' : '1200px'" :height="isFullscreen ? '100vh' : '500px'" :min-width="isFullscreen ? '100%' : '1200px'" :top="isFullscreen ? '0px' : '0px'" append-to-body :close-on-click-modal="false" :fullscreen="isFullscreen" @close="handleClose">
    <!-- 自定义头部 -->
    <div slot="title" class="dialog-header">
      <span class="dialog-title">{{ title }}</span>
      <div class="dialog-header-buttons">
        <el-button
          type="text"
          icon="el-icon-full-screen"
          @click="toggleFullscreen"
          class="fullscreen-btn"
          :title="isFullscreen ? '退出全屏' : '全屏'"
        ></el-button>
      </div>
    </div>
    
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="填报人" prop="userName">
            <el-input
              v-model="form.userName"
              placeholder="请输入填报人"
              :disabled="true"
              class="readonly-input"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="确认部门" prop="companyCode">
            <el-input
              v-model="form.companyCode"
              placeholder="请输入确认部门"
              :disabled="true"
              class="readonly-input"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请部门" prop="deptNo">
            <el-select
              v-model="form.deptNo"
              clearable
              placeholder="请选择申请部门"
              style="width: 100%"
              :disabled="isViewMode"
            >
              <el-option
                v-for="item in getDepNameList"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="供应商代码" prop="suppId">
            <el-input
              v-model="form.suppId"
              placeholder="请输入供应商代码"
              :disabled="isViewMode"
            >
              <i v-if="!isViewMode" slot="suffix" class="el-icon-search search-icon" @click="showSuppInfoDialog"></i>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="供应商名称" prop="suppName">
            <el-input v-model="form.suppName" placeholder="请输入供应商名称" :disabled="isViewMode" :class="{ 'readonly-input': isViewMode }" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="供应类型" prop="suppType">
            <el-select v-model="form.suppType" placeholder="请选择涉及货物、服务或工程" style="width: 100%" @change="handleMaterialOrServiceChange" :disabled="isViewMode">
              <el-option
                v-for="dict in suppTypeOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="处罚类型" prop="punishmentType">
            <el-select v-model="form.punishmentType" placeholder="请选择处罚类型" style="width: 100%" :disabled="isViewMode">
              <el-option
                v-for="dict in punishmentTypeOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="事件发生时间" prop="happenedTimeTemp">
            <el-date-picker
              v-model="form.happenedTimeTemp"
              type="date"
              placeholder="请选择事件发生时间"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              style="width: 100%"
              :disabled="isViewMode"
              :clearable="true"
              @change="handleDateChange">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="处罚执行时间" prop="punishmentTimeTemp">
            <el-date-picker
              v-model="form.punishmentTimeTemp"
              type="date"
              placeholder="请选择处罚执行时间"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              style="width: 100%"
              :disabled="isViewMode"
              :clearable="true"
              @change="handleDateChange">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 处罚依据标题 -->
      <el-row class="title-row">
        <el-col :span="24">
          <div class="section-title">
            <span><span style="color: #ff0000; font-size: 18px; font-weight: bold; margin-right: 4px; vertical-align: middle; position: relative; top: 2px;">*</span>处罚依据</span>
            <el-divider class="inline-divider"></el-divider>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="质量异议单号" prop="qualityNo">
            <el-input v-model="form.qualityNo" placeholder="请输入质量异议单号" :disabled="isViewMode" :class="{ 'readonly-input': isViewMode }" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="文件报批单号" prop="fileNo">
            <el-input v-model="form.fileNo" placeholder="请输入文件报批单号" :disabled="isViewMode" :class="{ 'readonly-input': isViewMode }" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="巡检处罚单号" prop="checkNo">
            <el-input v-model="form.checkNo" placeholder="请输入巡检处罚单号" :disabled="isViewMode" :class="{ 'readonly-input': isViewMode }" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="安管处罚单号" prop="securityNo">
            <el-input v-model="form.securityNo" placeholder="请输入安管处罚单号" :disabled="isViewMode" :class="{ 'readonly-input': isViewMode }" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="制度名称" prop="systemNo">
            <el-input v-model="form.systemNo" placeholder="请输入制度名称" :disabled="isViewMode" :class="{ 'readonly-input': isViewMode }" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="依据内容" prop="basisContent">
            <el-input v-model="form.basisContent" type="textarea" placeholder="请输入依据内容" :rows="3" :disabled="isViewMode" :class="{ 'readonly-input': isViewMode }" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 处罚措施标题 -->
      <el-row class="title-row">
        <el-col :span="24">
          <div class="section-title">
            <span><span style="color: #ff0000; font-size: 18px; font-weight: bold; margin-right: 4px; vertical-align: middle; position: relative; top: 2px;">*</span>处罚措施</span>
            <el-divider class="inline-divider"></el-divider>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="是否降级" prop="reduce">
            <el-select
              v-model="form.reduce"
              placeholder="请选择是否降级"
              clearable
              style="width: 100%"
              :disabled="isViewMode"
            >
              <el-option
                v-for="dict in yesNoOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>        
        
        <el-col :span="8">
          <el-form-item label="处罚金额(元)" prop="punishmentAmt">
            <el-input
              v-model="form.punishmentAmt"
              placeholder="请输入处罚金额(数字)"
              :disabled="isViewMode"
              :class="{ 'readonly-input': isViewMode }"
              @blur="formatPunishmentAmt"
              @input="onPunishmentAmtChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="暂缓(月)" prop="hold">
            <el-input-number
              v-model="form.hold"
              :min="0"
              :precision="0"
              placeholder="请输入暂缓月数"
              :disabled="isViewMode"
              style="width: 100%"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="是否淘汰" prop="pass">
            <el-select
              v-model="form.pass"
              placeholder="请选择是否淘汰"
              clearable
              style="width: 100%"
              :disabled="isViewMode"
            >
              <el-option
                v-for="dict in yesNoOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="处罚事由" prop="punishmentReason">
            <el-input v-model="form.punishmentReason" type="textarea" placeholder="请输入处罚事由" :rows="3" :disabled="isViewMode" :class="{ 'readonly-input': isViewMode }" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 闭环依据标题 -->
      <el-row class="title-row" v-if="shouldShowClosureFields">
        <el-col :span="24">
          <div class="section-title">
            <span><span style="color: #ff0000; font-size: 18px; font-weight: bold; margin-right: 4px; vertical-align: middle; position: relative; top: 2px;">*</span>闭环依据</span>
            <el-divider class="inline-divider"></el-divider>
          </div>
        </el-col>
      </el-row>
      <!-- 闭环依据：只有输入了处罚金额才显示 -->
      <el-row :gutter="20" v-if="shouldShowClosureFields">
        <el-col :span="8">
          <el-form-item label="发票号" prop="invoiceNo">
            <el-input v-model="form.invoiceNo" placeholder="请输入发票号" :disabled="isViewMode" :class="{ 'readonly-input': isViewMode }" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="处罚单号" prop="punishmentNo">
            <el-input v-model="form.punishmentNo" placeholder="请输入处罚单号" :disabled="isViewMode" :class="{ 'readonly-input': isViewMode }" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="!isViewMode" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">{{ isViewMode ? '关 闭' : '取 消' }}</el-button>
    </div>

    <!-- 供应商信息查询弹窗 -->
    <supp-info-dialog ref="suppInfoDialog" @select="handleSuppSelect" />

    <!-- 物料信息查询弹窗 -->
    <material-info-dialog ref="materialInfoDialog" @select="handleMaterialSelect" />

    <!-- 服务查询弹窗 -->
    <service-project-dialog ref="serviceDialog" @select="handleServiceSelect" />
    <!-- 项目查询弹窗 -->
    <project-dialog ref="projectDialog" @select="handleProjectSelect" />


  </el-dialog>
</template>

<script>
import { addPunishment, updatePunishment, getUserCompany } from "@/api/suppPunishment/punishment";
import SuppInfoDialog from "./suppInfo-module.vue";
import MaterialInfoDialog from "./materialInfo-module.vue";
import ServiceProjectDialog from "./service-module.vue";
import ProjectDialog from "./project-module.vue";

export default {
  name: "AddPunishmentModule",
  components: {
    SuppInfoDialog,
    MaterialInfoDialog,
    ServiceProjectDialog,
    ProjectDialog
  },
  props: {
    // 字典数据
    punishmentTypeOptions: {
      type: Array,
      default: () => []
    },
    suppTypeOptions: {
      type: Array,
      default: () => []
    },
    getDepNameList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 是否显示弹出层
      visible: false,
      // 弹出层标题
      title: "",
      // 是否全屏显示
      isFullscreen: false,
      // 是否为查看模式
      isViewMode: false,
      // 表单参数
      form: {},
      // 是否选项字典
      yesNoOptions: [],


      // 表单校验
      rules: {
        suppId: [
          { required: true, trigger: "blur", message: "请输入供应商代码" }
        ],
        suppName: [
          { required: true, trigger: "blur", message: "请输入供应商名称" }
        ],
        suppType: [
          { required: true, trigger: "change", message: "请选择货物或服务" }
        ],
        punishmentType: [
          { required: true, trigger: "change", message: "请选择处罚类型" }
        ],
        deptNo: [
          { required: true, trigger: "change", message: "请选择申请部门" }
        ],
        happenedTimeTemp: [
          { required: true, trigger: "blur", message: "请选择事件发生时间" }
        ],
        punishmentTimeTemp: [
          { required: true, trigger: "blur", message: "请选择处罚执行时间" }
        ],
        punishmentAmt: [
          {
            validator: (_, value, callback) => {
              if (!value) {
                callback(); // 允许为空
                return;
              }

              // 检查是否为有效数字
              const numValue = parseFloat(value);
              if (isNaN(numValue) || numValue < 0) {
                callback(new Error('请输入有效的数字金额'));
                return;
              }

              // 检查小数位数
              const str = value.toString();
              if (str.includes('.')) {
                const decimalPart = str.split('.')[1];
                if (decimalPart && decimalPart.length > 2) {
                  callback(new Error('金额最多保留2位小数'));
                  return;
                }
              }

              callback();
            },
            trigger: "blur"
          }
        ],

        punishmentReason: [
          { required: true, trigger: "blur", message: "请输入处罚事由" }
        ],
        basisContent: [
          { required: true, trigger: "blur", message: "请输入依据内容" }
        ],

      }
    };
  },

  created() {
    // 获取是否字典数据
    this.getDicts("sys_yes_no").then((response) => {
      this.yesNoOptions = response.data;
    });
  },

  computed: {
    // 判断是否应该显示闭环依据字段
    shouldShowClosureFields() {
      // 查看模式下始终显示
      if (this.isViewMode) {
        return true;
      }

      // 新增/编辑模式下，只有输入了处罚金额才显示
      const punishmentAmt = this.form.punishmentAmt;
      return punishmentAmt && punishmentAmt.toString().trim() !== '' && parseFloat(punishmentAmt) > 0;
    }
  },

  methods: {


    // 业务逻辑校验 - 新的写法
    checkBusinessRules() {
      console.log('=== 开始业务逻辑校验 ===');
      console.log('表单数据:', JSON.stringify(this.form, null, 2));
      console.log('临时日期数据:', {
        happenedTimeTemp: this.happenedTimeTemp,
        punishmentTimeTemp: this.punishmentTimeTemp
      });

      // 1. 处罚金额格式校验（新增和修改都需要）
      const punishmentAmt = this.form.punishmentAmt;
      if (punishmentAmt && punishmentAmt.toString().trim() !== '') {
        const amount = parseFloat(punishmentAmt);
        if (isNaN(amount)) {
          console.log('❌ 处罚金额格式错误');
          return '请输入有效的数字';
        }
        if (amount <= 0) {
          console.log('❌ 处罚金额必须大于0');
          return '处罚金额必须大于0';
        }
        console.log('✅ 处罚金额格式校验通过');
      }

      // 新增和修改都执行相同的校验
      const isEditMode = this.form.id && this.form.id !== null && this.form.id !== undefined;
      console.log('是否为修改模式:', isEditMode, '表单ID:', this.form.id);
      console.log('📝 执行完整业务逻辑校验（新增和修改都需要）');

      // 处罚依据校验
      const basisFields = [
        this.form.qualityNo,
        this.form.fileNo,
        this.form.checkNo,
        this.form.securityNo,
        this.form.systemNo
      ];

      let hasBasisField = false;
      for (let i = 0; i < basisFields.length; i++) {
        const field = basisFields[i];
        if (field && field.toString().trim() !== '') {
          hasBasisField = true;
          break;
        }
      }

      console.log('处罚依据字段:', basisFields);
      console.log('处罚依据校验结果:', hasBasisField);

      if (!hasBasisField) {
        console.log('❌ 处罚依据校验失败');
        return '至少需要填写一个处罚依据';
      }
      console.log('✅ 处罚依据校验通过');

      // 处罚措施校验
      const reduce = this.form.reduce;
      const pass = this.form.pass;
      console.log('是否降级:', reduce, '是否淘汰:', pass);

      if (reduce === 'N' && pass === 'N') {
        const hasAmount = punishmentAmt && punishmentAmt.toString().trim() !== '';
        const hasHold = this.form.hold && this.form.hold > 0;
        console.log('处罚金额有值:', hasAmount, '暂缓有值:', hasHold);

        if (!hasAmount && !hasHold) {
          console.log('❌ 处罚措施校验失败');
          return '至少需要填写一个处罚措施';
        }
        console.log('✅ 处罚措施校验通过');
      } else {
        console.log('✅ 跳过处罚措施校验（降级或淘汰不是N）');
      }

      // 闭环依据校验 - 只有在显示闭环依据字段时才校验
      if (this.shouldShowClosureFields && !this.isViewMode) {
        const invoiceNo = this.form.invoiceNo;
        const punishmentNo = this.form.punishmentNo;
        console.log('发票号:', invoiceNo, '处罚单号:', punishmentNo);

        const hasInvoice = invoiceNo && invoiceNo.toString().trim() !== '';
        const hasPunishmentNo = punishmentNo && punishmentNo.toString().trim() !== '';

        console.log('发票号有值:', hasInvoice, '处罚单号有值:', hasPunishmentNo);

        if (!hasInvoice && !hasPunishmentNo) {
          console.log('❌ 闭环依据校验失败');
          return '至少需要填写一个闭环依据';
        }
        console.log('✅ 闭环依据校验通过');
      } else {
        console.log('✅ 跳过闭环依据校验（字段未显示或为查看模式）');
      }

      console.log('🎉 所有业务逻辑校验通过');
      return null; // 返回null表示没有错误
    },

    // 调试日期变化
    handleDateChange(value) {
      console.log('日期变化:', value);
      console.log('当前form数据:', this.form);
      console.log('临时日期字段:', {
        happenedTimeTemp: this.form.happenedTimeTemp,
        punishmentTimeTemp: this.form.punishmentTimeTemp
      });
    },



    // 初始化临时日期变量
    initTempDates() {
      console.log('开始初始化临时日期:', {
        happenedTime: this.form.happenedTime,
        punishmentTime: this.form.punishmentTime
      });

      // 将yyyyMMdd格式转换为yyyy-MM-dd格式用于日期选择器显示
      if (this.form.happenedTime) {
        const dateStr = this.form.happenedTime.toString();
        if (dateStr.length === 8 && /^\d{8}$/.test(dateStr)) {
          // 确保是8位数字格式
          this.happenedTimeTemp = `${dateStr.substring(0, 4)}-${dateStr.substring(4, 6)}-${dateStr.substring(6, 8)}`;
        } else if (dateStr.includes('-')) {
          // 如果已经是yyyy-MM-dd格式，直接使用
          this.happenedTimeTemp = dateStr;
        } else {
          this.happenedTimeTemp = '';
        }
      } else {
        this.happenedTimeTemp = '';
      }

      if (this.form.punishmentTime) {
        const dateStr = this.form.punishmentTime.toString();
        if (dateStr.length === 8 && /^\d{8}$/.test(dateStr)) {
          // 确保是8位数字格式
          this.punishmentTimeTemp = `${dateStr.substring(0, 4)}-${dateStr.substring(4, 6)}-${dateStr.substring(6, 8)}`;
        } else if (dateStr.includes('-')) {
          // 如果已经是yyyy-MM-dd格式，直接使用
          this.punishmentTimeTemp = dateStr;
        } else {
          this.punishmentTimeTemp = '';
        }
      } else {
        this.punishmentTimeTemp = '';
      }

      console.log('初始化临时日期完成:', {
        happenedTime: this.form.happenedTime,
        happenedTimeTemp: this.happenedTimeTemp,
        punishmentTime: this.form.punishmentTime,
        punishmentTimeTemp: this.punishmentTimeTemp
      });

      // 强制更新视图
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },

    // 确保时间格式正确（简化版本，让后台处理格式转换）
    ensureTimeFormat() {
      // 直接将日期选择器的值赋给提交字段，让后台处理格式转换
      this.form.happenedTime = this.form.happenedTimeTemp;
      this.form.punishmentTime = this.form.punishmentTimeTemp;

      console.log('提交的时间数据:', {
        happenedTime: this.form.happenedTime,
        punishmentTime: this.form.punishmentTime
      });
    },

    // 显示对话框
    show(data = {}) {
      console.log('显示对话框，接收到的数据:', data);
      this.visible = true;

      if (data.isView) {
        this.reset();
        // 查看模式
        this.form = { ...data };
        this.title = "查看供应商处罚记录";
        this.isViewMode = true;

        console.log('查看模式设置:', {
          isView: data.isView,
          isViewMode: this.isViewMode,
          title: this.title
        });

        // 简单设置日期字段（index.vue已经转换为yyyy-MM-dd格式）
        this.$set(this.form, 'happenedTimeTemp', this.form.happenedTime || '');
        this.$set(this.form, 'punishmentTimeTemp', this.form.punishmentTime || '');

        // 强制更新视图
        this.$nextTick(() => {
          this.$forceUpdate();
        });

      } else if (data.id) {
        this.reset();
        // 修改模式
        this.form = { ...data };
        this.title = "修改供应商处罚记录";
        this.isViewMode = false;

        console.log('修改模式，表单数据:', this.form);

        // 简单设置日期字段（index.vue已经转换为yyyy-MM-dd格式）
        this.$set(this.form, 'happenedTimeTemp', this.form.happenedTime || '');
        this.$set(this.form, 'punishmentTimeTemp', this.form.punishmentTime || '');

        console.log('设置后的临时日期字段:', {
          happenedTimeTemp: this.form.happenedTimeTemp,
          punishmentTimeTemp: this.form.punishmentTimeTemp
        });

      } else {
        this.reset();
        // 新增模式
        this.title = "添加供应商处罚记录";
        this.isViewMode = false;
        // 初始化临时日期变量（新增模式下为空）
        this.$set(this.form, 'happenedTimeTemp', '');
        this.$set(this.form, 'punishmentTimeTemp', '');
        // 自动填入当前登录用户
        if (this.$store.getters.name) {
          this.getUserCompanyInfo(this.$store.getters.name);
        }
      }

      console.log('最终表单状态:', {
        form: this.form,
        happenedTimeTemp: this.happenedTimeTemp,
        punishmentTimeTemp: this.punishmentTimeTemp
      });
    },

    // 表单重置
    reset() {
      this.form = {
        id: null,
        recCreator: null,
        recCreateTime: null,
        recRevisor: null,
        recReviseTime: null,
        userName: null,
        serialNo: null,
        companyCode: null,
        deptNo: null,
        suppId: null,
        suppName: null,
        itemNo: null,
        itemName: null,
        punishmentType: null,
        suppType: null,
        happenedTime: null,
        punishmentTime: null,
        happenedTimeTemp: '',
        punishmentTimeTemp: '',
        happenedTimeTemp: '',
        punishmentTimeTemp: '',
        punishmentReason: null,
        reduce: 'N',  // 是否降级默认为N
        pass: 'N',    // 是否淘汰默认为N
        // 处罚依据相关字段
        qualityNo: null,
        fileNo: null,
        checkNo: null,
        securityNo: null,
        systemNo: null,
        basisContent: null,
        // 处罚措施相关字段
        punishmentAmt: null,
        hold: 0,  // 暂缓月数默认为0
        // 闭环依据相关字段
        invoiceNo: null,
        punishmentNo: null
      };

      // 重置视图状态
      this.isViewMode = false;
      this.title = "";

      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },

    // 取消按钮
    cancel() {
      this.visible = false;
      this.reset();
    },

    // 关闭对话框
    handleClose() {
      this.isFullscreen = false; // 重置全屏状态
      this.reset();
    },

    // 提交表单
    submitForm() {
      console.log('提交表单开始', this.form);

      this.$refs["form"].validate(valid => {
        console.log('表单校验结果:', valid);
        if (valid) {
          // 手动进行业务逻辑校验
          const businessError = this.checkBusinessRules();
          console.log('业务逻辑校验错误:', businessError);
          if (businessError) {
            this.$message.error(businessError);
            return;
          }

          // 确保时间字段格式正确
          this.ensureTimeFormat();

          console.log('准备提交数据:', this.form);
          if (this.form.id != null) {
            console.log('执行修改操作');
            updatePunishment(this.form).then(response => {
              console.log('修改成功响应:', response);
              this.$message.success("修改成功");
              this.visible = false;
              this.$emit('success');
            }).catch(error => {
              console.error('修改失败详细信息:', error);
              console.error('错误响应:', error.response);
              const errorMsg = error.response?.data?.msg || error.message || "修改失败，请检查数据后重试";
              this.$message.error(errorMsg);
            });
          } else {
            console.log('执行新增操作');
            addPunishment(this.form).then(response => {
              console.log('新增成功响应:', response);
              this.$message.success("新增成功");
              this.visible = false;
              this.$emit('success');
            }).catch(error => {
              console.error('新增失败详细信息:', error);
              console.error('错误响应:', error.response);
              const errorMsg = error.response?.data?.msg || error.message || "新增失败，请检查数据后重试";
              this.$message.error(errorMsg);
            });
          }
        } else {
          console.log('表单校验失败');
          this.$message.warning("请完善必填信息");
        }
      });
    },

    // 显示供应商信息查询弹窗
    showSuppInfoDialog() {
      this.$refs.suppInfoDialog.show();
    },

    // 处理供应商选择
    handleSuppSelect(suppInfo) {
      this.form.suppId = suppInfo.suppId;
      this.form.suppName = suppInfo.suppName;
    },

    // 处理物料选择
    handleMaterialSelect(materialInfo) {
      this.form.itemNo = materialInfo.itemId;
      this.form.itemName = materialInfo.itemName;
    },

    // 处理服务选择
    handleServiceSelect(serviceInfo) {
      this.form.itemNo = serviceInfo.serviceNo;
      this.form.itemName = serviceInfo.serviceName;
    },

    // 处理项目选择
    handleProjectSelect(projectInfo) {
      this.form.itemNo = projectInfo.projectNo;
      this.form.itemName = projectInfo.projectName;
    },

    // 处理货物或服务选择变化
    handleMaterialOrServiceChange(value) {
      // 清空相关字段
      if (value === 'M' || value === 'S' || value === 'P') {
        // 选择任何类型时，都清空itemNo和itemName，让用户重新选择
        this.form.itemNo = null;
        this.form.itemName = null;
      } else {
        // 未选择时，清空所有相关字段
        this.form.itemNo = null;
        this.form.itemName = null;
      }

      // 切换类型后，清除之前的验证错误信息
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['itemName']);
        }
      });
    },



    // 根据填报人工号获取名字、单位信息
    getUserCompanyInfo(userName) {
      getUserCompany(userName).then(response => {
        if (response.code === 200 && response.data) {
          // 从SysUser对象中取rsDeptName作为确认部门和申请部门
          const deptName = response.data.rsDeptName || '';
          this.form.companyCode = deptName;  // 确认部门
          this.form.deptNo = deptName;       // 申请部门，默认与确认部门一致
          this.form.userName = response.data.nickName || '';
        }
      }).catch(error => {
        console.warn('获取单位信息失败:', error);
        // 不显示错误提示，避免影响用户体验
      });
    },

    // 格式化处罚金额 - 失去焦点时处理
    formatPunishmentAmt() {
      if (!this.form.punishmentAmt) return;

      const originalValue = this.form.punishmentAmt.toString().trim();
      let value = originalValue;

      // 移除非数字字符（除了小数点）
      let cleanValue = value.replace(/[^\d.]/g, '');

      // 如果为空，清空字段并提示
      if (!cleanValue) {
        this.form.punishmentAmt = '';
        // 清空闭环依据字段
        this.clearClosureFields();
        if (originalValue) {
          this.$message.warning('输入的内容不是有效数字，已清空');
        }
        return;
      }

      // 确保只有一个小数点
      const parts = cleanValue.split('.');
      if (parts.length > 2) {
        cleanValue = parts[0] + '.' + parts.slice(1).join('');
      }

      // 限制小数点后最多2位
      let wasDecimalTruncated = false;
      if (parts.length === 2 && parts[1].length > 2) {
        cleanValue = parts[0] + '.' + parts[1].substring(0, 2);
        wasDecimalTruncated = true;
      }

      // 转换为数字再转回字符串，去除前导零
      const numValue = parseFloat(cleanValue);
      if (!isNaN(numValue)) {
        const formattedValue = numValue.toString();
        this.form.punishmentAmt = formattedValue;

        // 检查是否有格式化变化，给出相应提示
        if (originalValue !== formattedValue) {
          if (wasDecimalTruncated) {
            this.$message.info(`金额已格式化为：${formattedValue}（小数位已限制为2位）`);
          } else if (originalValue.replace(/[^\d.]/g, '') !== originalValue) {
            this.$message.info(`金额已格式化为：${formattedValue}（已移除非数字字符）`);
          } else if (originalValue !== formattedValue) {
            this.$message.info(`金额已格式化为：${formattedValue}`);
          }
        }
      } else {
        this.form.punishmentAmt = '';
        this.$message.warning('输入的内容不是有效数字，已清空');
      }
    },

    // 清空闭环依据字段
    clearClosureFields() {
      if (!this.isViewMode) {
        this.form.invoiceNo = null;
        this.form.punishmentNo = null;
      }
    },

    // 监听处罚金额变化
    onPunishmentAmtChange(value) {
      // 如果金额被清空或变为无效值，清空闭环依据字段
      if (!value || value.toString().trim() === '' || parseFloat(value) <= 0) {
        this.clearClosureFields();
      }
    },

    /** 切换全屏状态 */
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen;
    }
  }
};
</script>

<style scoped>
/* 自定义弹窗头部样式 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  position: relative;
}

.dialog-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}

.dialog-header-buttons {
  position: absolute;
  right: 30px; /* 给关闭按钮留出空间 */
  top: 15px; /* 与关闭按钮完全对齐 */
  display: flex;
  align-items: center;
  height: 0px; /* 确保容器高度与关闭按钮一致 */
}

.fullscreen-btn {
  padding: 0 !important;
  margin: 0 !important;
  color: #909399;
  font-size: 16px;
  width: 32px !important;
  height: 32px !important;
  border-radius: 4px;
  transition: all 0.3s;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 32px !important;
  vertical-align: top !important;
}

.fullscreen-btn:hover {
  color: #409EFF;
  background-color: #f5f7fa;
}

.fullscreen-btn:active {
  color: #337ecc;
  background-color: #e6f7ff;
}



/* 确保弹窗头部有足够的高度和正确的定位 */
::v-deep .el-dialog__header {
  padding: 20px 20px 15px;
  min-height: 60px;
  position: relative;
}

/* 确保关闭按钮位置正确 */
::v-deep .el-dialog__headerbtn {
  position: absolute !important;
  top: 20px !important;
  right: 20px !important;
  transform: none !important;
  width: 32px !important;
  height: 32px !important;
  display: block !important;
  z-index: 1000 !important;
}

::v-deep .el-dialog__headerbtn .el-dialog__close {
  color: #909399 !important;
  font-size: 16px !important;
  width: 32px !important;
  height: 32px !important;
  line-height: 32px !important;
  text-align: center !important;
}

/* 弹窗标题居中 */
.el-dialog__header {
  text-align: center !important;
}

.el-dialog__header .el-dialog__title {
  text-align: center !important;
  width: 100% !important;
  display: block !important;
}

/* 更强的选择器确保弹窗标题居中 */
.el-dialog .el-dialog__header .el-dialog__title {
  text-align: center !important;
  width: 100% !important;
  display: block !important;
  margin: 0 auto !important;
}

/* 使用深度选择器确保样式穿透 */
::v-deep .el-dialog__header {
  text-align: center !important;
}

::v-deep .el-dialog__title {
  text-align: center !important;
  width: 100% !important;
  display: block !important;
}

/* 搜索图标样式 */
.search-icon {
  cursor: pointer;
  color: #909399;
  padding: 0 8px;
  transition: color 0.3s;
}

.search-icon:hover {
  color: #409EFF;
}

/* 处罚措施输入框样式 */
.measure-input-wrapper {
  position: relative;
  min-height: 78px;
  height: 78px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #fff;
}

.measure-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 62px;
  max-height: 62px;
  overflow-y: auto;
  align-items: flex-start;
  align-content: flex-start;
}

.measure-tag {
  margin: 0;
  font-size: 14px;
  height: 28px;
  line-height: 26px;
  border-radius: 14px;
  padding: 0 12px;
  cursor: default;
  font-weight: 500;
}

.measure-tag .el-icon-close {
  margin-left: 6px;
  font-size: 12px;
}

.measure-placeholder {
  color: #c0c4cc;
  font-size: 14px;
  line-height: 1.5;
  position: absolute;
  top: 8px;
  left: 12px;
  cursor: pointer;
  user-select: none;
}

.measure-placeholder:hover {
  color: #409EFF;
}

.measure-textarea {
  width: 100%;
}

.measure-buttons {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  display: flex;
  gap: 5px;
}

.measure-btn {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 12px;
  min-width: 50px;
  height: 28px;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.measure-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* 选择按钮样式 */
.measure-btn.el-button--primary {
  background: rgba(64, 158, 255, 0.9);
  border-color: #409EFF;
  color: white;
}

.measure-btn.el-button--primary:hover {
  background: #409EFF;
  border-color: #409EFF;
}

/* 清空按钮样式 */
.measure-btn.el-button--danger {
  background: rgba(245, 108, 108, 0.9);
  border-color: #F56C6C;
  color: white;
}

.measure-btn.el-button--danger:hover {
  background: #F56C6C;
  border-color: #F56C6C;
}

/* 只读处罚措施输入框样式 */
.measure-textarea.el-textarea.is-disabled .el-textarea__inner,
.measure-textarea .el-textarea__inner[readonly] {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
  cursor: not-allowed;
}

/* 处罚依据输入框样式 */
.basis-input-wrapper {
  position: relative;
}

.basis-textarea {
  width: 100%;
}

.basis-buttons {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
}

.basis-btn {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 12px;
  min-width: 50px;
  height: 28px;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.basis-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* 选择按钮样式 */
.basis-btn.el-button--primary {
  background: rgba(64, 158, 255, 0.9);
  border-color: #409EFF;
  color: white;
}

.basis-btn.el-button--primary:hover {
  background: #409EFF;
  border-color: #409EFF;
}

/* 只读输入框样式 */
.readonly-input ::v-deep .el-input__inner {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #909399 !important;
  cursor: not-allowed !important;
}

.readonly-input ::v-deep .el-input__inner:hover {
  border-color: #e4e7ed !important;
}

.readonly-input ::v-deep .el-input__inner:focus {
  border-color: #e4e7ed !important;
  box-shadow: none !important;
}

/* 查看模式下所有禁用输入框的统一样式 - 更强的选择器优先级 */
::v-deep .el-form-item .el-input.is-disabled .el-input__inner,
::v-deep .el-form-item .el-input[disabled] .el-input__inner,
::v-deep .el-input.is-disabled .el-input__inner,
::v-deep .el-input[disabled] .el-input__inner {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #909399 !important;
  cursor: not-allowed !important;
}

::v-deep .el-form-item .el-input.is-disabled .el-input__inner:hover,
::v-deep .el-form-item .el-input[disabled] .el-input__inner:hover,
::v-deep .el-input.is-disabled .el-input__inner:hover,
::v-deep .el-input[disabled] .el-input__inner:hover {
  border-color: #e4e7ed !important;
}

::v-deep .el-form-item .el-textarea.is-disabled .el-textarea__inner,
::v-deep .el-form-item .el-textarea[disabled] .el-textarea__inner,
::v-deep .el-textarea.is-disabled .el-textarea__inner,
::v-deep .el-textarea[disabled] .el-textarea__inner {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #909399 !important;
  cursor: not-allowed !important;
}

::v-deep .el-form-item .el-select.is-disabled .el-input__inner,
::v-deep .el-form-item .el-select[disabled] .el-input__inner,
::v-deep .el-select.is-disabled .el-input__inner,
::v-deep .el-select[disabled] .el-input__inner {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #909399 !important;
  cursor: not-allowed !important;
}

::v-deep .el-form-item .el-input-number.is-disabled .el-input__inner,
::v-deep .el-form-item .el-input-number[disabled] .el-input__inner,
::v-deep .el-input-number.is-disabled .el-input__inner,
::v-deep .el-input-number[disabled] .el-input__inner {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #909399 !important;
  cursor: not-allowed !important;
}

::v-deep .el-form-item .el-date-editor.is-disabled .el-input__inner,
::v-deep .el-form-item .el-date-editor[disabled] .el-input__inner,
::v-deep .el-date-editor.is-disabled .el-input__inner,
::v-deep .el-date-editor[disabled] .el-input__inner {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #909399 !important;
  cursor: not-allowed !important;
}

/* 最强优先级 - 确保所有禁用状态的输入框都有一致的文字颜色 */
::v-deep input[disabled],
::v-deep textarea[disabled],
::v-deep .el-input__inner[disabled],
::v-deep .el-textarea__inner[disabled],
::v-deep .is-disabled input,
::v-deep .is-disabled textarea,
::v-deep .is-disabled .el-input__inner,
::v-deep .is-disabled .el-textarea__inner {
  color: #909399 !important;
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
}

/* 分组标题行样式 */
.title-row {
  margin-bottom: 0 !important;
}

.title-row .el-col {
  padding-bottom: 0;
}

/* 分组标题样式 */
.section-title {
  margin: 0;
  padding: 0px 0 15px 0;
  display: flex;
  align-items: center;
  height: auto;
  line-height: 0.1;
}

.section-title span {
  font-size: 13px;
  font-weight: bold;
  color: #606266;
  margin-right: 10px;
  white-space: nowrap;
}



.section-title .inline-divider {
  flex: 1;
  margin: 0;
}

/* 确保所有分割线样式一致，统一加粗 */
.section-title .inline-divider ::v-deep .el-divider__text {
  background-color: #fff;
  padding: 0;
}

.section-title .inline-divider ::v-deep .el-divider--horizontal {
  border-top: 2px solid #dcdfe6 !important;
  border-width: 2px !important;
  border-bottom: none !important;
}

/* 更强的选择器确保所有分割线都加粗 */
.section-title ::v-deep .el-divider {
  border-top: 2px solid #dcdfe6 !important;
  border-width: 2px !important;
  border-bottom: none !important;
  margin: 0 !important;
}

.section-title ::v-deep .el-divider.el-divider--horizontal {
  border-top: 2px solid #dcdfe6 !important;
  border-width: 2px !important;
}

/* 针对所有标题行中的分割线，统一加粗 */
.title-row ::v-deep .el-divider {
  border-top: 2px solid #dcdfe6 !important;
  border-width: 2px !important;
  border-bottom: none !important;
  margin: 0 !important;
}

.title-row ::v-deep .el-divider--horizontal {
  border-top: 2px solid #dcdfe6 !important;
  border-width: 2px !important;
}

/* 减少按钮上方的空白 */
.dialog-footer {
  margin-top: 0px !important;
  padding-top: 5px !important;
}

/* 减少表单底部的边距 */
.el-form {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* 减少最后一行表单项的底部间距 */
.el-form .el-row:last-child {
  margin-bottom: 0 !important;
}

.el-form .el-row:last-child .el-form-item {
  margin-bottom: 0 !important;
}
</style>
