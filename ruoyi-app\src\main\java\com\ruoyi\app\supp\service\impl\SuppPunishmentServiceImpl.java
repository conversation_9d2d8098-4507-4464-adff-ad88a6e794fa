package com.ruoyi.app.supp.service.impl;

import java.time.LocalDateTime;
import java.util.*;

import com.ruoyi.app.domain.MaterialInfo;
import com.ruoyi.app.enums.SuppPunishmentEnum;
import com.ruoyi.app.enums.SuppPunishmentGroupEnum;
import com.ruoyi.app.enums.SuppPunishmentTypeEnum;
import com.ruoyi.app.supp.domain.ServiceProject;
import com.ruoyi.app.supp.domain.SuppInfo;
import com.ruoyi.app.supp.domain.SuppUser;
import com.ruoyi.app.supp.mapper.SuppPunishmentDepMapper;
import com.ruoyi.app.supp.service.ISuppPunishmentService;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.supp.mapper.SuppPunishmentMapper;
import com.ruoyi.app.supp.domain.SuppPunishment;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.text.SimpleDateFormat;
import java.util.Date;
/**
 * 供应商处罚Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
public class SuppPunishmentServiceImpl implements ISuppPunishmentService
{
    @Autowired
    private SuppPunishmentMapper suppPunishmentMapper;
    @Autowired
    private SuppPunishmentDepMapper suppPunishmentDepMapper;
    @Autowired
    private ISysUserService userService;

    private static int counter = 0;
    /**
     * 查询供应商处罚
     *
     * @param id 供应商处罚ID
     * @return 供应商处罚
     */
    @Override
    public SuppPunishment selectSuppPunishmentById(Long id)
    {
        return suppPunishmentMapper.selectSuppPunishmentById(id);
    }

    /**
     * 查询供应商处罚列表
     *
     * @param suppPunishment 供应商处罚
     * @return 供应商处罚
     */
    @Override
    public List<SuppPunishment> selectSuppPunishmentList(SuppPunishment suppPunishment)
    {
        // 查阅组、管理组可以看到所有
        SuppUser suppUser = new SuppUser();
        suppUser.setUserNo(SecurityUtils.getLoginUser().getUsername());
        SuppUser user = suppPunishmentMapper.selectUserGroupByUserName(suppUser);
        if (user.getUserGroup().equals(SuppPunishmentGroupEnum.CONFIRM.getCode())||user.getUserGroup().equals(SuppPunishmentGroupEnum.INPUT.getCode())){
            // 根据登录人部门匹配确认部门
            suppPunishment.setCompanyCode(SecurityUtils.getLoginUser().getUser().getRsDeptName());
        }
        return suppPunishmentMapper.selectSuppPunishmentList(suppPunishment);
    }

    /**
     * 查询供应商信息列表
     * */
    @Override
    public List<SuppInfo> selectSuppInfoList(SuppInfo suppInfo) {
        return suppPunishmentMapper.selectSuppInfoList(suppInfo);
    }

    @Override
    public List<MaterialInfo> selectMaterialInfoList(MaterialInfo materialInfo) {
        return suppPunishmentMapper.selectMaterialInfoList(materialInfo);
    }

    @Override
    public List<ServiceProject> selectServiceList(ServiceProject serviceProject) {
        return suppPunishmentMapper.selectServiceList(serviceProject);
    }
    @Override
    public List<ServiceProject> selectProjectList(ServiceProject serviceProject) {
        return suppPunishmentMapper.selectProjectList(serviceProject);
    }

    @Override
    public SuppUser selectUserGroupByUserName() {
        SuppUser suppUser = new SuppUser();
        suppUser.setUserNo(SecurityUtils.getLoginUser().getUsername());
        return suppPunishmentMapper.selectUserGroupByUserName(suppUser);
    }

    @Override
    public List<String> selectDepNameList() {
        return suppPunishmentDepMapper.selectDepNameList();
    }

    /**
     * 新增供应商处罚
     *
     * @param suppPunishment 供应商处罚
     * @return 结果
     */
    @Override
    public int insertSuppPunishment(SuppPunishment suppPunishment)
    {
        suppPunishment.setStateId(SuppPunishmentEnum.DRAFT.getCode());//int默认是0，这行可以不加
        suppPunishment.setRecCreateTime(DateUtils.getTime());
        suppPunishment.setRecCreator(SecurityUtils.getLoginUser().getUsername());//登录人工号
        suppPunishment.setUserNo(SecurityUtils.getLoginUser().getUsername());//申请人工号：登录人工号
        //申请人名字、申请人部门名称已经在前台赋值
        //suppPunishment.setUserName(userService.selectUserByUserName(SecurityUtils.getUsername()).getNickName());//申请人名字：登录人名字
        return suppPunishmentMapper.insertSuppPunishment(suppPunishment);
    }

    /**
     * 修改供应商处罚
     *
     * @param suppPunishment 供应商处罚
     * @return 结果
     */
    @Override
    public int updateSuppPunishment(SuppPunishment suppPunishment)
    {
        suppPunishment.setRecReviseTime(DateUtils.getTime());
        suppPunishment.setRecRevisor(SecurityUtils.getLoginUser().getUsername());
        return suppPunishmentMapper.updateSuppPunishment(suppPunishment);
    }

    /**
     * 批量删除供应商处罚
     *
     * @param ids 需要删除的供应商处罚ID
     * @return 结果
     */
    @Override
    public int deleteSuppPunishmentByIds(Long[] ids)
    {
        return suppPunishmentMapper.deleteSuppPunishmentByIds(ids);
    }

    /**
     * 删除供应商处罚信息
     *
     * @param id 供应商处罚ID
     * @return 结果
     */
    @Override
    public int deleteSuppPunishmentById(Long id)
    {
        return suppPunishmentMapper.deleteSuppPunishmentById(id);
    }

    /**
     * 批量确认
     *
     * @param ids 供应商处罚ID
     * @return 结果
     */
    @Override
    public Object confirmSuppPunishmentByIds(Long[] ids)
    {
        for (Long id:ids) {
            SuppPunishment suppPunishment = new SuppPunishment();
            suppPunishment.setId(id);
            suppPunishment.setStateId(SuppPunishmentEnum.CONFIRM.getCode());
            suppPunishment.setRecReviseTime(DateUtils.getTime());
            suppPunishment.setSerialNo(generateSerialNO());
//            SecurityUtils.getUsername();//获取登录人工号
//            SecurityUtils.getLoginUser().getUsername();
            suppPunishment.setConfirmNo(SecurityUtils.getUsername());
            suppPunishment.setConfirmName(userService.selectUserByUserName(SecurityUtils.getUsername()).getNickName());
            // 针对导入的数据，确认部门为空，自动带出
            SuppPunishment suppPunishmentExist = suppPunishmentMapper.selectSuppPunishmentById(id);
            if(suppPunishmentExist.getCompanyCode().isEmpty()|| suppPunishmentExist.getCompanyCode().equals("")||suppPunishmentExist.getCompanyCode().equals(" ")){
                String rsDeptName = userService.selectUserByUserName(SecurityUtils.getUsername()).getRsDeptName();
                suppPunishment.setCompanyCode(rsDeptName);
            }
            suppPunishment.setConfirmTime(DateUtils.dateTimeNow());
            suppPunishmentMapper.confirmSuppPunishmentById(suppPunishment);
        }
        return null;
    }

    /**
     * 生成编号
     * @return
     */
    public String generateSerialNO() {
        String result = "";
        // 获取当前月份
        Calendar cal = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String currentMonth = sdf.format(cal.getTime());

        //判断当月有没有已确认的，如果没有，从001开始；如果有，根据确认时间（添加字段）找出最新的，在后面+1
        SuppPunishment suppPunishment = suppPunishmentMapper.selectSuppPunishmentSerialNo();
        if (suppPunishment!=null){
            String serialNo = suppPunishment.getSerialNo();
            String sub=serialNo.substring(serialNo.length()-3);
            // 将字符串转换为整数并加1
            int number = Integer.parseInt(sub) + 1;

            // 将整数转换回字符串，并保持前导零
            String newString = String.format("%03d", number);
            result = result+"GYSCF" + currentMonth + newString;
        }else{
            result = result+"GYSCF" + currentMonth +"001";
        }
        return result;
    }



}
