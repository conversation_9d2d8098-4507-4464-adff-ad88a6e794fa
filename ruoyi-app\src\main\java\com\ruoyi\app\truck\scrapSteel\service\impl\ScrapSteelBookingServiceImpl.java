package com.ruoyi.app.truck.scrapSteel.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ruoyi.app.external.sms.constants.SmsTemplateConstant;
import com.ruoyi.app.external.sms.dto.RecordDTO;
import com.ruoyi.app.external.sms.utils.SmsUtils;
import com.ruoyi.app.truck.scrapSteel.domain.*;
import com.ruoyi.app.truck.scrapSteel.enums.HasDelayEnum;
import com.ruoyi.app.truck.scrapSteel.enums.ScrapMaterialEnum;
import com.ruoyi.app.truck.scrapSteel.enums.ScrapSteelDoorEnum;
import com.ruoyi.app.truck.scrapSteel.enums.ScrapSteelStatusEnum;
import com.ruoyi.app.truck.scrapSteel.mapper.*;
import com.ruoyi.app.truck.scrapSteel.service.IScrapSteelAiSuoService;
import com.ruoyi.app.truck.scrapSteel.service.ScrapSteelBookingService;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisLockEntity;
import com.ruoyi.common.core.redis.RedisLockUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.SnowFlakeUtil;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.mybatis.spring.SqlSessionTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.io.UnsupportedEncodingException;
import java.sql.Time;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/9 下午3:53
 */
@Service
public class ScrapSteelBookingServiceImpl implements ScrapSteelBookingService {
    private static final Logger log = LoggerFactory.getLogger(ScrapSteelBookingServiceImpl.class);

    @Value("${server.env}")
    private String env;

    @Autowired
    private RedisLockUtils redisLockUtils;

    @Autowired
    private ScrapSteelOrderMapper scrapSteelOrderMapper;

    @Autowired
    private ScrapSteelEverydayConfigMapper scrapSteelEverydayConfigMapper;

    @Autowired
    private ScrapSteelLogMapper scrapSteelLogMapper;

    @Autowired
    private ScrapSteelLimitConfigMapper scrapSteelLimitConfigMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private IScrapSteelAiSuoService scrapSteelAiSuoService;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private SqlSessionTemplate sqlSessionTemplate;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private ScrapSteelRequestRecordMapper scrapSteelRequestRecordMapper;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    // ThreadLocal存储booking结果
    private static final ThreadLocal<BookingResult> BOOKING_RESULT_THREAD_LOCAL = new ThreadLocal<>();

    // booking结果内部类
    public static class BookingResult {
        private boolean success;
        private String failReason;
        private String ip;
        private long startTime;
        private long endTime;

        public BookingResult(boolean success, String failReason, String ip, long startTime, long endTime) {
            this.success = success;
            this.failReason = failReason;
            this.ip = ip;
            this.startTime = startTime;
            this.endTime = endTime;
        }

        // getters
        public boolean isSuccess() { return success; }
        public String getFailReason() { return failReason; }
        public String getIp() { return ip; }
        public long getStartTime() { return startTime; }
        public long getEndTime() { return endTime; }
    }

    // 获取booking结果
    public static BookingResult getBookingResult() {
        return BOOKING_RESULT_THREAD_LOCAL.get();
    }

    // 清理ThreadLocal
    public static void clearBookingResult() {
        BOOKING_RESULT_THREAD_LOCAL.remove();
    }

    /**
     * 取号
     * 注意点：调用接口前，请将废钢入厂单相关信息赋值（料型、时间段、预约日期）
     * 场景1：废钢入厂单申请时
     * 场景2：废钢入厂单灵活调整时
     * @param ticketNo 废钢入厂单号
     * @param isForce 是否强制
     */
    public Pair<Boolean, String> booking(String ticketNo, boolean isForce){
        ScrapSteelOrder scrapSteelOrder = scrapSteelOrderMapper.selectScrapSteelOrderByTicketNo(ticketNo);
        if(Objects.isNull(scrapSteelOrder)){
            throw new RuntimeException("废钢入厂单不存在");
        }
        return this.booking(scrapSteelOrder, isForce);
    }

    /**
     * 取号
     *      * 注意点：调用接口前，请将废钢入厂单相关信息赋值（料型、时间段、预约日期）
     *      * 场景1：废钢入厂单申请时
     *      * 场景2：废钢入厂单灵活调整时
     * @param scrapSteelOrder
     * @param isForce
     */
    public Pair<Boolean, String> booking(ScrapSteelOrder scrapSteelOrder, boolean isForce){
        //抢号结果
        boolean result = false;
        //失败原因
        String failReason = "";
        //加锁时间
        long startTime = System.currentTimeMillis();
        String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        //1.获取锁  redisKey命名规范：{env}.{module}.{business}.{businessII}.{businessIII}...  例如dev.scrapSteel.20240510.1
        //2.根据料型获取目前还剩余的车数
        ScrapSteelEverydayConfig scrapSteelEverydayConfig = this.getScrapSteelEverydayConfig(scrapSteelOrder);
        if(Objects.isNull(scrapSteelEverydayConfig)){
            failReason = "当前排队预约配置不存在";
            return Pair.of(result, failReason);
        }
        //锁key
        RedisLockEntity redisLockEntity = new RedisLockEntity(this.getRedisKey(scrapSteelEverydayConfig), UUID.randomUUID().toString());

        try {
            log.info("预约单号"+scrapSteelOrder.getTicketNo()+"开始抢号...");
            //打印供应商名、车牌、身份证
            log.info("预约抢号信息---供应商："+scrapSteelOrder.getApplyCompanyName()+"，车牌："+scrapSteelOrder.getCarNo()+
                    "，身份证："+scrapSteelOrder.getDriverCardNo()+"，IP地址："+ip);
            if(!isForce){
                if (scrapSteelEverydayConfig.getCurrentTruckNum() >= scrapSteelEverydayConfig.getMaxTruckNum()) {
                    log.error("该料型每天允许的车数已超出");
                    failReason = "该料型每天允许的车数已超出";
                    return Pair.of(result, failReason);
                }
                if (!this.checkTruckNum(scrapSteelOrder)) {
                    log.error("该供应商该料型每天允许的车数已超出");
                    failReason = "该供应商该料型每天允许的车数已超出";
                    return Pair.of(result, failReason);
                }
                String unfinishedTicketNo = checkUnfinishedScrapSteelOrder(scrapSteelOrder);
                if(StringUtils.isNotBlank(unfinishedTicketNo)){
                    log.error("当前车辆"+scrapSteelOrder.getCarNo()+"今天还有未完成的预约单");
                    failReason = "车辆存在未完成的废钢预约单，单号"+unfinishedTicketNo+"，请先完成该预约单再二次预约";
                    return Pair.of(result, failReason);
                }
            }

            if(redisLockUtils.tryLock(redisLockEntity)) {
                //双重检测锁
                scrapSteelEverydayConfig = this.getScrapSteelEverydayConfig(scrapSteelOrder);
                //3.非强制调整时，判断该供应商该料型每天允许的车数是否超出
                if(!isForce){
                    if (scrapSteelEverydayConfig.getCurrentTruckNum() >= scrapSteelEverydayConfig.getMaxTruckNum()) {
                        log.error("该料型每天允许的车数已超出");
                        failReason = "该料型每天允许的车数已超出";
                        return Pair.of(result, failReason);
                    }
                    if (!this.checkTruckNum(scrapSteelOrder)) {
                        log.error("该供应商该料型每天允许的车数已超出");
                        failReason = "该供应商该料型每天允许的车数已超出";
                        return Pair.of(result, failReason);
                    }
                    String unfinishedTicketNo = checkUnfinishedScrapSteelOrder(scrapSteelOrder);
                    if(StringUtils.isNotBlank(unfinishedTicketNo)){
                        log.error("当前车辆"+scrapSteelOrder.getCarNo()+"今天还有未完成的预约单");
                        failReason = "当前车辆"+scrapSteelOrder.getCarNo()+"今天还有未完成的预约单";
                        return Pair.of(result, failReason);
                    }
                }

                //4.当天该料型已预约的车数+1
                scrapSteelEverydayConfigMapper.addScrapSteelEverydayConfigNum(scrapSteelEverydayConfig);

                //5.记录日志
                if (!isForce) {
                    String info = "废钢入厂单：" + scrapSteelOrder.getTicketNo() + "，预约号预占成功";
                    this.insertScrapSteelLog(scrapSteelOrder, info);
                }

                result = true;
                return Pair.of(result, "预约成功");
            }else{
                log.error("当前请求人数过多，请重试，废钢预约单编号为"+scrapSteelOrder.getTicketNo());
                failReason = "当前请求人数过多，请重试";
                throw new RuntimeException("当前请求人数过多，请重试");
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("取号失败，系统发生异常",e);
            //如果failReason为空，说明是系统异常
            if(StringUtils.isBlank(failReason)){
                failReason = "系统异常";
            }
            //继续向外抛出异常
            throw e;
        }finally {
            //释放锁
            redisLockUtils.unlockLua(redisLockEntity);
            //解锁时间
            long endTime = System.currentTimeMillis();
            //耗时
            log.info("预约单号"+scrapSteelOrder.getTicketNo()+"结束抢号...");
            log.info("抢号耗时：{}ms，订单信息：{}",(endTime - startTime), JSON.toJSONString(scrapSteelOrder));
            
            // 将booking结果存入ThreadLocal
            BOOKING_RESULT_THREAD_LOCAL.set(new BookingResult(result, failReason, ip, startTime, endTime));
        }

    }

    /**
     * 获取车辆当天未完成的预约单（进行中，非驳回状态）
     */
    public String checkUnfinishedScrapSteelOrder(ScrapSteelOrder scrapSteelOrder){
        //根据配置判断是否开启车辆是否离厂判断
        String isOpen = sysConfigService.selectConfigByKey("truck.scrapSteel.checkCarLeave");
        if("1".equals(isOpen)){
            ScrapSteelOrder condition = new ScrapSteelOrder();
            condition.setScrapSteelStatusList(Lists.newArrayList(ScrapSteelStatusEnum.WAIT_LEAVE.getCode(), ScrapSteelStatusEnum.WAIT_ENTER.getCode(),
                    ScrapSteelStatusEnum.WAIT_BUSINESS_REVIEW.getCode(), ScrapSteelStatusEnum.WAIT_ENV_REVIEW.getCode()));
            condition.setCarNo(scrapSteelOrder.getCarNo());
            List<ScrapSteelOrder> waitLeaveScrapSteelOrderList = scrapSteelOrderMapper.selectScrapSteelOrderList(condition);
            if(CollectionUtils.isNotEmpty(waitLeaveScrapSteelOrderList) && waitLeaveScrapSteelOrderList.size() >= 2){
                String waitFinishTicketNo = waitLeaveScrapSteelOrderList.stream().filter(x->!x.getTicketNo().equals(scrapSteelOrder.getTicketNo())).findFirst().get().getTicketNo();
                return waitFinishTicketNo;
//                throw new RuntimeException("车辆存在未完成的废钢预约单，单号"+waitFinishTicketNo+"，请先完成该预约单再二次预约");
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 校验供应商每天的限制车数
     * 1.获取该供应商当天该料型已安排的车数
     * 2.获取该供应商当天该料型每天限制车数
     * 3.判断是否超出
     */
    public boolean checkTruckNum(ScrapSteelOrder scrapSteelOrder){
        //获取该供应商当天该料型每天限制车数
        ScrapSteelLimitConfig config = new ScrapSteelLimitConfig();
        config.setCompanyId(scrapSteelOrder.getApplyCompanyId());
        config.setSteelDesc(scrapSteelOrder.getSteelDesc());
        List<ScrapSteelLimitConfig> scrapSteelLimitConfigList = scrapSteelLimitConfigMapper.selectScrapSteelLimitConfigList(config);
        if(CollectionUtils.isEmpty(scrapSteelLimitConfigList)){
            //该供应商当天该料型每天限制车数未设置
            return true;
        }
        ScrapSteelLimitConfig scrapSteelLimitConfig = scrapSteelLimitConfigList.get(0);
        long maxTruckNum = scrapSteelLimitConfig.getLimitNum();

        ScrapSteelOrder condition = new ScrapSteelOrder();
        condition.setSteelDesc(scrapSteelOrder.getSteelDesc());
        condition.setEnterFactoryDate(scrapSteelOrder.getEnterFactoryDate());
        condition.setApplyCompanyId(scrapSteelOrder.getApplyCompanyId());
        List<ScrapSteelOrder> list = scrapSteelOrderMapper.selectScrapSteelOrderList(condition);
        //废钢入厂单状态： 1-待商务部审核 2-待能环部审核 3-待入厂 4-已入厂 5-已离厂 11-已驳回
        //获取该供应商当天该料型已安排的车数
        long currentNum = list.stream().filter(x->x.getStatus().intValue() != ScrapSteelStatusEnum.REJECT.getCode()).count();

        if(currentNum > maxTruckNum){
            return false;
        }
        return true;
    }

    /**
     * 获取当天该料型排号信息
     * @param scrapSteelOrder
     * @return
     */
    private ScrapSteelEverydayConfig getScrapSteelEverydayConfig(ScrapSteelOrder scrapSteelOrder){
        sqlSessionTemplate.clearCache();
        ScrapSteelEverydayConfig scrapSteelEverydayConfig = new ScrapSteelEverydayConfig();
        scrapSteelEverydayConfig.setSteelDesc(scrapSteelOrder.getSteelDesc());
        scrapSteelEverydayConfig.setCurrentQueueDate(scrapSteelOrder.getEnterFactoryDate());
        scrapSteelEverydayConfig.setPeriodStartTime(scrapSteelOrder.getPeriodStartTime());
        scrapSteelEverydayConfig.setPeriodEndTime(scrapSteelOrder.getPeriodEndTime());

        List<ScrapSteelEverydayConfig> list = scrapSteelEverydayConfigMapper.selectScrapSteelEverydayConfigList(scrapSteelEverydayConfig);

        if(CollectionUtils.isEmpty(list) || list.size() >  1){
            throw new RuntimeException("该料型当天("+DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, scrapSteelOrder.getEnterFactoryDate())+")排号信息异常");
        }

        return list.get(0);
   }

    /**
     * 释放号
     * 场景1：废钢入厂单审核被驳回时
     * 场景2：废钢入厂单灵活调整时
     * @param ticketNo 废钢入厂单号
     */
    @Override
    public void release(String ticketNo) {
        ScrapSteelOrder scrapSteelOrder = scrapSteelOrderMapper.selectScrapSteelOrderByTicketNo(ticketNo);
        if(Objects.isNull(scrapSteelOrder)){
            throw new RuntimeException("废钢入厂单不存在");
        }
        if(Objects.isNull(scrapSteelOrder.getPeriodStartTime())){
            throw new RuntimeException("废钢入厂单当前排队信息为空");
        }

        //1.获取锁  redisKey命名规范：{env}.{module}.{business}.{businessII}.{businessIII}...  例如dev.scrapSteel.20240510.1
        RedisLockEntity redisLockEntity = new RedisLockEntity(this.getRedisKey(this.getScrapSteelEverydayConfig(scrapSteelOrder)), UUID.randomUUID().toString());
        try {
            if(redisLockUtils.tryLock(redisLockEntity)) {
                //2.根据料型获取目前还剩余的车数
                ScrapSteelEverydayConfig scrapSteelEverydayConfig = this.getScrapSteelEverydayConfig(scrapSteelOrder);
                //3.当天该料型已预约的车数+1
                scrapSteelEverydayConfig.setCurrentTruckNum(scrapSteelEverydayConfig.getCurrentTruckNum() - 1);
                scrapSteelEverydayConfigMapper.updateScrapSteelEverydayConfig(scrapSteelEverydayConfig);
                //4.记录日志
                String info = "废钢入厂单：" + scrapSteelOrder.getTicketNo() + "，预约号释放成功";
                this.insertScrapSteelLog(scrapSteelOrder, info);
            }else{
                throw new RuntimeException("驳回失败，请重试");
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("取号失败",e);
        }finally {
            //释放锁
            redisLockUtils.unlockLua(redisLockEntity);
        }
    }

    @Override
    public void bookingSuccess(String ticketNo) {
        ScrapSteelOrder scrapSteelOrder = scrapSteelOrderMapper.selectScrapSteelOrderByTicketNo(ticketNo);
        if(Objects.isNull(scrapSteelOrder)){
            throw new RuntimeException("废钢入厂单不存在");
        }
        //1.记录日志
        String info = "废钢入厂单："+scrapSteelOrder.getTicketNo()+"，取号成功";
        this.insertScrapSteelLog(scrapSteelOrder,info);
        //2.短信通知
        this.sendBookingSuccessSms(scrapSteelOrder);
    }

    /**
     * 查询预约信息
     * @param currentDate
     * @return
     */
    @Override
    public List<ScrapSteelQueueInfo> queryQueueInfo(String currentDate) {
        List<ScrapSteelQueueInfo> scrapSteelQueueInfoList = new ArrayList<>();

        //判断是否为当天
        boolean isSameDay = true;
        if(!DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDateYYYYMMDD()).equals(currentDate)){
            isSameDay = false;
        }
        
        if(StringUtils.isBlank(currentDate)){
            return Lists.newArrayList();
        }
        ScrapSteelEverydayConfig condition =new ScrapSteelEverydayConfig();
        condition.setCurrentQueueDate(DateUtils.parseDate(currentDate));
        List<ScrapSteelEverydayConfig> scrapSteelEverydayConfigList = scrapSteelEverydayConfigMapper.selectScrapSteelEverydayConfigList(condition);
        if(CollectionUtils.isEmpty(scrapSteelEverydayConfigList)){
            return Lists.newArrayList();
        }
        Map<String, List<ScrapSteelEverydayConfig>> map = scrapSteelEverydayConfigList.stream().collect(Collectors.groupingBy(ScrapSteelEverydayConfig::getSteelDesc));
        for (String info : map.keySet()) {
            ScrapMaterialEnum scrapMaterialEnum = ScrapMaterialEnum.getByInfo(info);
            ScrapSteelQueueInfo scrapSteelQueueInfo = new ScrapSteelQueueInfo();
            scrapSteelQueueInfo.setCode(scrapMaterialEnum.getCode());
            scrapSteelQueueInfo.setInfo(scrapMaterialEnum.getInfo());

            List<ScrapSteelEverydayConfig> list = map.get(info);
            if(isSameDay){
                list = list.stream()
                        .filter(x->DateUtils.minutesDifferenceFromNow(x.getPeriodEndTime()) < 0)//过期时间小于当前时间
                        .map(x->this.handleScrapSteelEverydayConfiginfo(x))
                        .collect(Collectors.toList());
            }else{
                list = list.stream()
                        .map(x->this.handleScrapSteelEverydayConfiginfo(x))
                        .collect(Collectors.toList());
            }

            scrapSteelQueueInfo.setScrapSteelEverydayConfigList(list);
            scrapSteelQueueInfoList.add(scrapSteelQueueInfo);
        }
        return scrapSteelQueueInfoList;
    }

    private ScrapSteelEverydayConfig handleScrapSteelEverydayConfiginfo(ScrapSteelEverydayConfig scrapSteelEverydayConfig){
        scrapSteelEverydayConfig.setPeriodStartTimeStr(DateUtils.parseTimeToStr(DateUtils.HH_MM_SS, scrapSteelEverydayConfig.getPeriodStartTime()));
        scrapSteelEverydayConfig.setPeriodEndTimeStr(DateUtils.parseTimeToStr(DateUtils.HH_MM_SS, scrapSteelEverydayConfig.getPeriodEndTime()));
        scrapSteelEverydayConfig.setCurrentQueueDateStr(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, scrapSteelEverydayConfig.getCurrentQueueDate()));

        return scrapSteelEverydayConfig;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelQueue(String ticketNo, String updateWorkNo) {
        try{
            SysUser sysUser = sysUserMapper.selectUserByUserName(updateWorkNo);
            ScrapSteelOrder scrapSteelOrder = scrapSteelOrderMapper.selectScrapSteelOrderByTicketNo(ticketNo);
            if(Objects.isNull(scrapSteelOrder)){
                throw new RuntimeException("废钢入厂单不存在");
            }
            Time oldStartTime = scrapSteelOrder.getPeriodStartTime();
            Time oldEndTime = scrapSteelOrder.getPeriodEndTime();

            //释放排队号
            this.release(ticketNo);
            //将排队信息置为空
            //将状态置为已取消
            scrapSteelOrder.setStatus(ScrapSteelStatusEnum.CANCEL.getCode());
            scrapSteelOrderMapper.cancelQueue(scrapSteelOrder);
            String info = "废钢入厂单："+scrapSteelOrder.getTicketNo()+"，"+sysUser.getNickName()+"取消排队成功";
            this.insertScrapSteelLog(scrapSteelOrder,info);

            scrapSteelAiSuoService.pushDeleteTruckInfo(scrapSteelOrder);
            //短信通知
            this.sendCancelQueueSms(scrapSteelOrder, oldStartTime, oldEndTime);
        }catch (Exception e){
            e.printStackTrace();
            log.error("取消排队失败",e);
            throw new RuntimeException("取消排队失败，请重试");
        }

    }

    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    @Override
    public void flexibleQueue(String ticketNo, String updateWorkNo, String startTime, String endTime) throws UnsupportedEncodingException {
        try{
            boolean isNeedCancel = false;
            ScrapSteelOrder scrapSteelOrder = scrapSteelOrderMapper.selectScrapSteelOrderByTicketNo(ticketNo);
            String oldQueueNo = scrapSteelOrder.getQueueNo();
            String newQueueNo = SnowFlakeUtil.getScrapSteelTicketQueueNoSnowFlakeId();
            Time oldStartTime = scrapSteelOrder.getPeriodStartTime();
            Time oldEndTime = scrapSteelOrder.getPeriodEndTime();
            //1.取消现有的排队
            if(Objects.nonNull(scrapSteelOrder.getPeriodStartTime())){
                isNeedCancel = true;
            }
            if(isNeedCancel){
                this.cancelQueue(ticketNo,updateWorkNo);
            }


            //2.插入新的排队
            scrapSteelOrder.setStatus(ScrapSteelStatusEnum.WAIT_ENTER.getCode());
            scrapSteelOrder.setPeriodStartTime(DateUtils.convertToTime(startTime));
            scrapSteelOrder.setPeriodEndTime(DateUtils.convertToTime(endTime));
            scrapSteelOrder.setQueueNo(newQueueNo);//新的排队号
            String predictEntryBeginDate = combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), scrapSteelOrder.getPeriodStartTime());
            String predictEntryEndDate = combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), scrapSteelOrder.getPeriodEndTime());
            scrapSteelOrder.setPredictEntryBeginDate(DateUtils.parseDate(predictEntryBeginDate));
            scrapSteelOrder.setPredictEntryEndDate(DateUtils.parseDate(predictEntryEndDate));
            this.booking(scrapSteelOrder,true);
            //3.更新订单信息
            scrapSteelOrderMapper.updateScrapSteelOrder(scrapSteelOrder);


            //5.爱索相关接口触发
            try{
                scrapSteelOrder.setQueueNo(oldQueueNo);
                scrapSteelAiSuoService.pushDeleteTruckInfo(scrapSteelOrder);

                scrapSteelOrder.setQueueNo(newQueueNo);
                scrapSteelAiSuoService.pushTruckInfo(scrapSteelOrder);
            }catch (Exception e){
                e.printStackTrace();
                throw new RuntimeException("爱索相关接口触发失败");
            }

            if(isNeedCancel){
                this.sendCancelQueueSms(scrapSteelOrder, oldStartTime, oldEndTime);
            }

            //4.取号成功通知及日志
            this.bookingSuccess(ticketNo);
        }catch (Exception e){
            e.printStackTrace();
            log.error("插队失败",e);
            throw e;
        }

    }

    @Override
    public void leaveManually(String ticketNo, String updateWorkNo) throws UnsupportedEncodingException {
        SysUser sysUser = sysUserMapper.selectUserByUserName(updateWorkNo);
        ScrapSteelOrder scrapSteelOrder = scrapSteelOrderMapper.selectScrapSteelOrderByTicketNo(ticketNo);
        if(Objects.isNull(scrapSteelOrder)){
            throw new RuntimeException("废钢入厂单不存在");
        }
        if(scrapSteelOrder.getStatus()!=ScrapSteelStatusEnum.WAIT_LEAVE.getCode()){
            throw new RuntimeException("废钢入厂单状态错误，请刷新");
        }
        scrapSteelOrder.setStatus(ScrapSteelStatusEnum.FINISHED.getCode());
        scrapSteelOrder.setIsLeaveManually(1);
        scrapSteelOrderMapper.updateScrapSteelOrder(scrapSteelOrder);
        String info = "废钢入厂单："+scrapSteelOrder.getTicketNo()+"，"+sysUser.getNickName()+"手动出厂成功";
        this.insertScrapSteelLog(scrapSteelOrder,info);
    }

    @Override
    public void enterManually(String ticketNo, String updateWorkNo) throws UnsupportedEncodingException {
        SysUser sysUser = sysUserMapper.selectUserByUserName(updateWorkNo);
        ScrapSteelOrder scrapSteelOrder = scrapSteelOrderMapper.selectScrapSteelOrderByTicketNo(ticketNo);
        if(Objects.isNull(scrapSteelOrder)){
            throw new RuntimeException("废钢入厂单不存在");
        }
        if(scrapSteelOrder.getStatus()!=ScrapSteelStatusEnum.WAIT_ENTER.getCode()){
            throw new RuntimeException("废钢入厂单状态错误，请刷新");
        }
        scrapSteelOrder.setStatus(ScrapSteelStatusEnum.WAIT_LEAVE.getCode());
        scrapSteelOrder.setIsEnterManually(1);
        scrapSteelOrderMapper.updateScrapSteelOrder(scrapSteelOrder);
        String info = "废钢入厂单："+scrapSteelOrder.getTicketNo()+"，"+sysUser.getNickName()+"手动入厂成功";
        this.insertScrapSteelLog(scrapSteelOrder,info);
    }

    private String getRedisKey(ScrapSteelEverydayConfig scrapSteelEverydayConfig){
        String currentEnv = StringUtils.isBlank(env)?"dev":env;
        StringBuilder sb = new StringBuilder();
        sb.append(currentEnv);
        sb.append(".");
        sb.append("scrapSteel");
        sb.append(".");
        sb.append("config");
        sb.append(".");
        sb.append(scrapSteelEverydayConfig.getId());
        return sb.toString();
    }

    private void insertScrapSteelLog(ScrapSteelOrder scrapSteelOrder, String info){
        ScrapSteelLog scrapSteelLog = new ScrapSteelLog();
        scrapSteelLog.setTicketNo(scrapSteelOrder.getTicketNo());
        scrapSteelLog.setInfo(info);
        scrapSteelLog.setCreateBy(scrapSteelOrder.getCreateBy());
        scrapSteelLog.setCreateTime(new Date());

        scrapSteelLogMapper.insertScrapSteelLog(scrapSteelLog);
    }

    /**
     * "tpContent":{
     *                 "companyName":"携程网络技术有限公司",
     *                 "carNo":"苏B61BH2",
     *                 "doorName":"滨江0号门",
     *                 "startTime":"2024-05-09 08:00:00",
     *                 "endTime":"2024-05-09 09:00:00"
     *             }
     * @param scrapSteelOrder
     */
    private void sendBookingSuccessSms(ScrapSteelOrder scrapSteelOrder){
        List<RecordDTO> recordDTOS = Lists.newArrayList();
        RecordDTO recordDTO = new RecordDTO();
        recordDTO.setMobile(scrapSteelOrder.getSupplierSalesPhone());
        Map<String, String> tpContent = new HashMap<>();
        tpContent.put("companyName",scrapSteelOrder.getApplyCompanyName());
        tpContent.put("carNo",scrapSteelOrder.getCarNo());
        tpContent.put("doorName", ScrapSteelDoorEnum.getEnumByCode(scrapSteelOrder.getEnterDoor()).getDesc());
        tpContent.put("startTime",this.combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), scrapSteelOrder.getPeriodStartTime()));
        tpContent.put("endTime",this.combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), scrapSteelOrder.getPeriodEndTime()));
        tpContent.put("ticketNo", scrapSteelOrder.getTicketNo());
        recordDTO.setTpContent(tpContent);
        recordDTOS.add(recordDTO);
        SmsUtils.sendSms(SmsTemplateConstant.BOOKING_SUCCESS_TEMPLATE_ID,recordDTOS);
    }

    /**
     * 取消排队短信通知
     */
    private void sendCancelQueueSms(ScrapSteelOrder scrapSteelOrder, Time startTime, Time endTime){
        List<RecordDTO> recordDTOS = Lists.newArrayList();
        RecordDTO recordDTO = new RecordDTO();
        recordDTO.setMobile(scrapSteelOrder.getSupplierSalesPhone());
        Map<String, String> tpContent = new HashMap<>();
        tpContent.put("companyName",scrapSteelOrder.getApplyCompanyName());
        tpContent.put("carNo",scrapSteelOrder.getCarNo());
        tpContent.put("startTime",this.combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), startTime));
        tpContent.put("endTime",this.combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), endTime));
        tpContent.put("ticketNo", scrapSteelOrder.getTicketNo());
        recordDTO.setTpContent(tpContent);
        recordDTOS.add(recordDTO);
        SmsUtils.sendSms(SmsTemplateConstant.CANCEL_QUEUE_TEMPLATE_ID,recordDTOS);
    }

    /**
     * 将日期部分和时间部分组合成一个字符串，格式为"yyyy-MM-dd HH:mm:ss"。
     *
     * @param enterFactoryDate 日期部分，Date类型
     * @param periodTime 时间部分，Time类型
     * @return 日期和时间组合后的字符串，如果输入为null，则返回null
     */
    public String combineDateAndTime(Date enterFactoryDate, Time periodTime) {
        if (enterFactoryDate == null || periodTime == null) {
            return null;
        }

        SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat timeFormatter = new SimpleDateFormat("HH:mm:ss");

        // 获取日期部分的字符串表示
        String datePart = dateFormatter.format(enterFactoryDate);

        // 获取时间部分的字符串表示
        String timePart = timeFormatter.format(new Date(periodTime.getTime()));

        // 组合日期和时间
        String combinedDateTimeStr = datePart + " " + timePart;

        return combinedDateTimeStr;
    }

    /**
     *废钢入厂第一次过号通知
     */
    public void sendFirstExpiredSms(ScrapSteelOrder scrapSteelOrder){
        List<RecordDTO> recordDTOS = Lists.newArrayList();
        RecordDTO recordDTO = new RecordDTO();
        recordDTO.setMobile(scrapSteelOrder.getSupplierSalesPhone());
        Map<String, String> tpContent = new HashMap<>();
        tpContent.put("companyName",scrapSteelOrder.getApplyCompanyName());
        tpContent.put("carNo",scrapSteelOrder.getCarNo());
        tpContent.put("startTime",this.combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), scrapSteelOrder.getPeriodStartTime()));
        tpContent.put("endTime",this.combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), scrapSteelOrder.getPeriodEndTime()));
        tpContent.put("ticketNo", scrapSteelOrder.getTicketNo());
        recordDTO.setTpContent(tpContent);
        recordDTOS.add(recordDTO);
        SmsUtils.sendSms(SmsTemplateConstant.FIRST_EXPIRED_TEMPLATE_ID,recordDTOS);
    }

    /**
     * 定时任务：到预约时间前5分钟，短信通知
     */
    public void sendCallingSms(){
//        ScrapSteelOrder condition = new ScrapSteelOrder();
//        condition.setEnterFactoryDate(DateUtils.dateTime(DateUtils.YYYY_MM_DD, DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD)));
//        condition.setStatus(ScrapSteelStatusEnum.WAIT_ENTER.getCode());
//        List<ScrapSteelOrder> list = scrapSteelOrderMapper.selectScrapSteelOrderList(condition);
//        for (ScrapSteelOrder scrapSteelOrder : list) {
//            if(Objects.isNull(scrapSteelOrder.getPeriodStartTime())){
//                continue;
//            }
//            //判断当前时间是否在预约时间前5分钟
//            int result = DateUtils.minutesDifferenceFromNow(scrapSteelOrder.getPeriodStartTime());
//            if(result <= 0 && result >= -5){
//                //发送叫号通知短信
//                List<RecordDTO> recordDTOS = Lists.newArrayList();
//                RecordDTO recordDTO = new RecordDTO();
//                recordDTO.setMobile(scrapSteelOrder.getSupplierSalesPhone());
//                Map<String, String> tpContent = new HashMap<>();
//                tpContent.put("companyName",scrapSteelOrder.getApplyCompanyName());
//                tpContent.put("carNo",scrapSteelOrder.getCarNo());
//                tpContent.put("doorName", ScrapSteelDoorEnum.getEnumByCode(scrapSteelOrder.getEnterDoor()).getDesc());
//                tpContent.put("startTime",this.combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), scrapSteelOrder.getPeriodStartTime()));
//                tpContent.put("endTime",this.combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), scrapSteelOrder.getPeriodEndTime()));
//                tpContent.put("ticketNo", scrapSteelOrder.getTicketNo());
//                recordDTO.setTpContent(tpContent);
//                recordDTOS.add(recordDTO);
//                SmsUtils.sendSms(SmsTemplateConstant.CALLING_TEMPLATE_ID,recordDTOS);
//            }
//
//        }
    }

    /**
     * 定时任务，过号通知短信
     */
    public void scrapSteelTaskExpired(){
        log.info("开始执行废钢排队过期任务...");
        //获取已经超出入厂时间段但仍未进厂的订单
        List<ScrapSteelOrder> list = scrapSteelOrderMapper.selectExpiredScrapSteelOrderList();
        for (ScrapSteelOrder scrapSteelOrder : list) {
            log.info("开始处理预约单号"+scrapSteelOrder.getTicketNo()+"的过期流程...");
            // 手动开启事务
            // 获取事务定义
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            // 设置事务隔离级别
            def.setIsolationLevel(TransactionDefinition.ISOLATION_DEFAULT);
            // 设置事务传播行为
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
            // 获取事务对象
            TransactionStatus transactionStatus = transactionManager.getTransaction(def);

            try{
                if(Objects.isNull(scrapSteelOrder.getPeriodStartTime())){
                    if(DateUtils.getNowDateYYYYMMDD().compareTo(scrapSteelOrder.getEnterFactoryDate()) > 0){
                        orderExpired(scrapSteelOrder);
                        transactionManager.commit(transactionStatus);
                        continue;
                    }
                    log.info("预约单号"+scrapSteelOrder.getTicketNo()+"已取消排队，跳过过期流程");
                    transactionManager.commit(transactionStatus);
                    continue;
                }
                //不同状态预约单的处理
                this.scrapSteelSingleTaskExpired(scrapSteelOrder);

                // 事务提交
                transactionManager.commit(transactionStatus);
            }catch (Exception e){
                e.printStackTrace();
                log.error("任务异常", e);
                // 事务回滚
                transactionManager.rollback(transactionStatus);
            }
            log.info("结束处理预约单号"+scrapSteelOrder.getTicketNo()+"的过期流程...");
        }
        log.info("结束执行废钢排队过期任务...");
    }

    public void scrapSteelSingleTaskExpired(ScrapSteelOrder scrapSteelOrder){
        //不同状态预约单的处理
        ScrapSteelStatusEnum statusEnum = ScrapSteelStatusEnum.getByCode(scrapSteelOrder.getStatus());
        switch (statusEnum){
            case WAIT_BUSINESS_REVIEW:
            case WAIT_ENV_REVIEW:
                this.orderExpired(scrapSteelOrder);
                break;
            case WAIT_ENTER:
                String isOpen = sysConfigService.selectConfigByKey("truck.scrapSteel.orderDelay");
                if("1".equals(isOpen)){
                    this.waitEnterExpired(scrapSteelOrder);
                }else{
                    this.orderExpired(scrapSteelOrder);
                }
                break;
            default:
                //do noting
                break;
        }
    }

    public void orderExpired(ScrapSteelOrder scrapSteelOrder){
        String startTime = this.combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), scrapSteelOrder.getPeriodStartTime());
        String endTime = this.combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), scrapSteelOrder.getPeriodEndTime());
        this.orderExpired(scrapSteelOrder, startTime, endTime);
    }

    public void waitEnterExpired(ScrapSteelOrder scrapSteelOrder){
        //1.尝试延号
        String startTime = this.combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), scrapSteelOrder.getPeriodStartTime());
        String endTime = this.combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), scrapSteelOrder.getPeriodEndTime());
        boolean delayResult = this.orderDelay(scrapSteelOrder);
        if(!delayResult){
            //2.预约号顺延失败，修改预约单状态，并直接发送过号通知短信
            this.orderExpired(scrapSteelOrder, startTime, endTime);

        }
    }

    /**
     * TODO 事务隔离
     * 1.预约号过期时，将该号后延至时间最近的下一个没有车辆预约的时间段
     * 2.若无法找到下一个时间段，则设置该预约单为过期，并发送过号通知短信
     * @param scrapSteelOrder
     */
    public boolean orderDelay(ScrapSteelOrder scrapSteelOrder){
        //1.是否延过号
        HasDelayEnum hasDelayEnum = HasDelayEnum.getByCode(scrapSteelOrder.getHasDelay());
        if(hasDelayEnum.equals(HasDelayEnum.YES)){
            log.info("预约单"+scrapSteelOrder.getTicketNo()+"已延过号，跳过延号流程");
            return false;
        }
        //2.判断预约单的入厂日期是否与当前日期一致，不一致则跳过延号流程
        if(scrapSteelOrder.getEnterFactoryDate().compareTo(DateUtils.getNowDateYYYYMMDD()) != 0){
            log.info("预约单"+scrapSteelOrder.getTicketNo()+"预约日期与当前日期不一致，跳过延号流程");
            return false;
        }
        //2.查询当天所有的时间段数据
        ScrapSteelEverydayConfig condition = new ScrapSteelEverydayConfig();
        condition.setCurrentQueueDate(scrapSteelOrder.getEnterFactoryDate());
        condition.setSteelDesc(scrapSteelOrder.getSteelDesc());
        List<ScrapSteelEverydayConfig> list = scrapSteelEverydayConfigMapper.selectScrapSteelEverydayConfigList(condition);
        //3.过滤出车数未满的时间段且预约时间段在当前时间之后
        List<ScrapSteelEverydayConfig> availableList = list.stream().filter(item -> item.getCurrentTruckNum() < item.getMaxTruckNum()
                && DateUtils.minutesDifferenceFromNow(item.getPeriodEndTime()) < 0)
                .sorted((Comparator.comparing(ScrapSteelEverydayConfig::getPeriodStartTime)))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(availableList)){
            log.info("预约单"+scrapSteelOrder.getTicketNo()+"延号失败，当前时间段已满");
            return false;
        }
        boolean delay = false;
        for (ScrapSteelEverydayConfig scrapSteelEverydayConfig : availableList) {
            String newQueueNo = SnowFlakeUtil.getScrapSteelTicketQueueNoSnowFlakeId();
            scrapSteelOrder.setPeriodStartTime(scrapSteelEverydayConfig.getPeriodStartTime());
            scrapSteelOrder.setPeriodEndTime(scrapSteelEverydayConfig.getPeriodEndTime());
            scrapSteelOrder.setQueueNo(newQueueNo);//新的排队号
            String predictEntryBeginDate = combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), scrapSteelOrder.getPeriodStartTime());
            String predictEntryEndDate = combineDateAndTime(scrapSteelOrder.getEnterFactoryDate(), scrapSteelOrder.getPeriodEndTime());
            scrapSteelOrder.setPredictEntryBeginDate(DateUtils.parseDate(predictEntryBeginDate));
            scrapSteelOrder.setPredictEntryEndDate(DateUtils.parseDate(predictEntryEndDate));
            scrapSteelOrder.setHasDelay(HasDelayEnum.YES.getCode());

            Pair<Boolean, String> result = this.booking(scrapSteelOrder, false);
            if(result.getLeft()){
                log.info("预约号过期后延成功");
                delay = true;
                //4.更新订单信息
                scrapSteelOrderMapper.updateScrapSteelOrder(scrapSteelOrder);
                this.insertScrapSteelLog(scrapSteelOrder, "废钢入厂单延号成功，已顺延至"+predictEntryBeginDate+"~"+predictEntryEndDate);
                //5.通知爱索
                scrapSteelAiSuoService.pushTruckInfo(scrapSteelOrder);
                //6.发送短信通知
                this.sendFirstExpiredSms(scrapSteelOrder);
                break;
            }
        }
        if(!delay){
            log.info("预约号过期后延失败，当前时间段已满");
            return delay;
        }

        return delay;
    }

    public void orderExpired(ScrapSteelOrder scrapSteelOrder, String startTime, String endTime){
        ScrapSteelOrder order = scrapSteelOrderMapper.selectScrapSteelOrderByTicketNo(scrapSteelOrder.getTicketNo());
        order.setStatus(ScrapSteelStatusEnum.EXPIRED.getCode());
        scrapSteelOrderMapper.updateScrapSteelOrder(order);

        this.insertScrapSteelLog(scrapSteelOrder, "废钢入厂单已过期");

        //判断进厂时间是否为当天，是则发送过期短信
//        if(DateUtils.getNowDateYYYYMMDD().compareTo(scrapSteelOrder.getEnterFactoryDate()) == 0){
//            List<RecordDTO> recordDTOS = Lists.newArrayList();
//            RecordDTO recordDTO = new RecordDTO();
//            recordDTO.setMobile(scrapSteelOrder.getSupplierSalesPhone());
//            Map<String, String> tpContent = new HashMap<>();
//            tpContent.put("companyName",scrapSteelOrder.getApplyCompanyName());
//            tpContent.put("carNo",scrapSteelOrder.getCarNo());
//            tpContent.put("startTime",startTime);
//            tpContent.put("endTime",endTime);
//            tpContent.put("ticketNo", scrapSteelOrder.getTicketNo());
//            recordDTO.setTpContent(tpContent);
//            recordDTOS.add(recordDTO);
//            SmsUtils.sendSms(SmsTemplateConstant.SECOND_EXPIRED_TEMPLATE_ID,recordDTOS);
//        }
    }

}
