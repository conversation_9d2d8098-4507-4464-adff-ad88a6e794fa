package com.ruoyi.app.qualityCost.service.impl;

import com.ruoyi.app.qualityCost.domain.*;
import com.ruoyi.app.qualityCost.mapper.CpTdDGxJzgydeMapper;
import com.ruoyi.app.qualityCost.mapper.DashboardMapper;
import com.ruoyi.app.qualityCost.service.IDashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Service
public class DashboardServiceImpl implements IDashboardService {

    @Autowired
    private DashboardMapper dashboardMapper;

    @Autowired
    private CpTdDGxJzgydeMapper cpTdDGxJzgydeMapper;

    @Override
    public PieChart getPieChartData(QualityCostDetail qualityCostDetail) {
        PieChart pieChart = new PieChart();

        List<QualityCostDetail> qualityCostDetails = dashboardMapper.selectQualityCostDetailList(qualityCostDetail);

        if (qualityCostDetail.getContainType() == 1) {
            for (QualityCostDetail costDetail : qualityCostDetails) {
                if (costDetail.getTypeCode().equals("A")) {
                    pieChart.setPreventionCost(costDetail.getCostEx());
                } else if (costDetail.getTypeCode().equals("B")) {
                    pieChart.setAppraisalCost(costDetail.getCostEx());
                }else if (costDetail.getTypeCode().equals("C")) {
                    pieChart.setInternalCost(costDetail.getCostEx());
                }else if (costDetail.getTypeCode().equals("D")) {
                    pieChart.setExternalCost(costDetail.getCostEx());
                }
            }
        }else{
            for (QualityCostDetail costDetail : qualityCostDetails) {
                if (costDetail.getTypeCode().equals("A")) {
                    pieChart.setPreventionCost(costDetail.getAllcEx());
                } else if (costDetail.getTypeCode().equals("B")) {
                    pieChart.setAppraisalCost(costDetail.getAllcEx());
                }else if (costDetail.getTypeCode().equals("C")) {
                    pieChart.setInternalCost(costDetail.getAllcEx());
                }else if (costDetail.getTypeCode().equals("D")) {
                    pieChart.setExternalCost(costDetail.getAllcEx());
                }
            }
        }


        return pieChart;
    }

    @Override
    public MultiLineChart getMultiLineChartData(QualityCostDetail qualityCostDetail) {

        Map<String, BigDecimal> preventionCostMap = new HashMap<>();
        Map<String,BigDecimal> appraisalCostMap = new HashMap<>();
        Map<String,BigDecimal> internalCostMap = new HashMap<>();
        Map<String,BigDecimal> externalCostMap = new HashMap<>();
        String yearMonth = qualityCostDetail.getYearMonth();

        //月份获取
        int year = Integer.parseInt(yearMonth.substring(0, 4));
        int month = Integer.parseInt(yearMonth.substring(4, 6));

        List<String> yearMonthList = new ArrayList<>(6);
        int totalMonths = year * 12 + month; // 转换为总月份数（便于计算）

        // 生成最近6个月（从最早到最新）
        for (int i = 5; i >= 0; i--) {
            int targetTotal = totalMonths - i; // 计算目标总月份数

            // 转换回年月格式
            int targetYear = (targetTotal - 1) / 12;
            int targetMonth = (targetTotal - 1) % 12 + 1;

            // 格式化为YYYYMM
            yearMonthList.add(String.format("%d%02d", targetYear, targetMonth));
        }

        if (qualityCostDetail.getContainType() == 1) {
            for (String element : yearMonthList) {
                qualityCostDetail.setYearMonth(element);

                List<QualityCostDetail> qualityCostDetails = dashboardMapper.selectQualityCostDetailList(qualityCostDetail);

                for (QualityCostDetail costDetail : qualityCostDetails) {
                    if (costDetail.getTypeCode().equals("A")) {
                        preventionCostMap.put(element, costDetail.getCostEx());
                    } else if (costDetail.getTypeCode().equals("B")) {
                        appraisalCostMap.put(element, costDetail.getCostEx());
                    }else if (costDetail.getTypeCode().equals("C")) {
                        internalCostMap.put(element, costDetail.getCostEx());
                    }else if (costDetail.getTypeCode().equals("D")) {
                        externalCostMap.put(element, costDetail.getCostEx());
                    }
                }
            }
        }else {
            for (String element : yearMonthList) {
                qualityCostDetail.setYearMonth(element);

                List<QualityCostDetail> qualityCostDetails = dashboardMapper.selectQualityCostDetailList(qualityCostDetail);

                for (QualityCostDetail costDetail : qualityCostDetails) {
                    if (costDetail.getTypeCode().equals("A")) {
                        preventionCostMap.put(element, costDetail.getAllcEx());
                    } else if (costDetail.getTypeCode().equals("B")) {
                        appraisalCostMap.put(element, costDetail.getAllcEx());
                    }else if (costDetail.getTypeCode().equals("C")) {
                        internalCostMap.put(element, costDetail.getAllcEx());
                    }else if (costDetail.getTypeCode().equals("D")) {
                        externalCostMap.put(element, costDetail.getAllcEx());
                    }
                }
            }
        }


        MultiLineChart multiLineChart = new MultiLineChart();
        multiLineChart.setPreventionCostMap(preventionCostMap);
        multiLineChart.setAppraisalCostMap(appraisalCostMap);
        multiLineChart.setInternalCostMap(internalCostMap);
        multiLineChart.setExternalCostMap(externalCostMap);

        return multiLineChart;
    }

    @Override
    public ComboChart getComboChartDetail(QualityCostDetail qualityCostDetail) {
        Map<String, BigDecimal> failureCostMap = new HashMap<>();
        Map<String,BigDecimal> controllingCostMap = new HashMap<>();

        String yearMonth = qualityCostDetail.getYearMonth();

        //月份获取
        int year = Integer.parseInt(yearMonth.substring(0, 4));
        int month = Integer.parseInt(yearMonth.substring(4, 6));

        List<String> yearMonthList = new ArrayList<>(6);
        int totalMonths = year * 12 + month; // 转换为总月份数（便于计算）

        // 生成最近6个月（从最早到最新）
        for (int i = 5; i >= 0; i--) {
            int targetTotal = totalMonths - i; // 计算目标总月份数

            // 转换回年月格式
            int targetYear = (targetTotal - 1) / 12;
            int targetMonth = (targetTotal - 1) % 12 + 1;

            // 格式化为YYYYMM
            yearMonthList.add(String.format("%d%02d", targetYear, targetMonth));
        }

        if (qualityCostDetail.getContainType() == 1) {
            for (String element : yearMonthList) {
                qualityCostDetail.setYearMonth(element);

                List<QualityCostDetail> qualityCostDetails = dashboardMapper.selectQualityCostDetailList(qualityCostDetail);


                BigDecimal failureCost = BigDecimal.ZERO;
                BigDecimal controllingCost = BigDecimal.ZERO;

                for (QualityCostDetail costDetail : qualityCostDetails) {

                    if (costDetail.getTypeCode().equals("A")) {
                        controllingCost = controllingCost.add(costDetail.getCostEx());
                    }
                    if (costDetail.getTypeCode().equals("B")) {
                        controllingCost = controllingCost.add(costDetail.getCostEx());
                    }
                    if (costDetail.getTypeCode().equals("C")) {
                        failureCost = failureCost.add(costDetail.getCostEx());
                    }
                    if (costDetail.getTypeCode().equals("D")) {
                        failureCost = failureCost.add(costDetail.getCostEx());
                    }
                }
                failureCostMap.put(element, failureCost);
                controllingCostMap.put(element, controllingCost);

            }
        } else {
            for (String element : yearMonthList) {
                qualityCostDetail.setYearMonth(element);

                List<QualityCostDetail> qualityCostDetails = dashboardMapper.selectQualityCostDetailList(qualityCostDetail);


                BigDecimal failureCost = BigDecimal.ZERO;
                BigDecimal controllingCost = BigDecimal.ZERO;

                for (QualityCostDetail costDetail : qualityCostDetails) {

                    if (costDetail.getTypeCode().equals("A")) {
                        controllingCost = controllingCost.add(costDetail.getAllcEx());
                    }
                    if (costDetail.getTypeCode().equals("B")) {
                        controllingCost = controllingCost.add(costDetail.getAllcEx());
                    }
                    if (costDetail.getTypeCode().equals("C")) {
                        failureCost = failureCost.add(costDetail.getAllcEx());
                    }
                    if (costDetail.getTypeCode().equals("D")) {
                        failureCost = failureCost.add(costDetail.getAllcEx());
                    }
                }
                failureCostMap.put(element, failureCost);
                controllingCostMap.put(element, controllingCost);

            }
        }


        ComboChart comboChart = new ComboChart();
        comboChart.setControllingCostMap(controllingCostMap);
        comboChart.setFailureCostMap(failureCostMap);

        return comboChart;
    }

    @Override
    public WaterfallChart getWaterfallChartDetail(QualityCostDetail qualityCostDetail) {
        List<QualityCostDetail> qualityCostDetails = dashboardMapper.selectQualityCostDetailList(qualityCostDetail);

        HashMap<String, BigDecimal> rescueProject = new HashMap<>();

        List<CpTdDGxJzgyde> cpTdDGxJzgydes = cpTdDGxJzgydeMapper.selectCpTdDGxJzgydeList(new CpTdDGxJzgyde());

        HashMap<String, String> convertMap = new HashMap<>();
        for (CpTdDGxJzgyde cpTdDGxJzgyde : cpTdDGxJzgydes) {
            convertMap.put(cpTdDGxJzgyde.getFjzgyMc(), cpTdDGxJzgyde.getFjzgy());
        }


        if (qualityCostDetail.getContainType() == 1) {
            for (QualityCostDetail costDetail : qualityCostDetails) {
                if (this.isValidC4String(costDetail.getTypeCode())) {
                    if (convertMap.get(costDetail.getTypeName()) == null || convertMap.get(costDetail.getTypeName()).isEmpty()) {
                        rescueProject.put(costDetail.getTypeName(), costDetail.getCostEx());
                    } else {
                        rescueProject.put(convertMap.get(costDetail.getTypeName()), costDetail.getCostEx());
                    }

                }
            }
        } else {
            for (QualityCostDetail costDetail : qualityCostDetails) {
                if (this.isValidC4String(costDetail.getTypeCode())) {
                    if (convertMap.get(costDetail.getTypeName()) == null || convertMap.get(costDetail.getTypeName()).isEmpty()) {
                        rescueProject.put(costDetail.getTypeName(), costDetail.getAllcEx());
                    } else {
                        rescueProject.put(convertMap.get(costDetail.getTypeName()), costDetail.getAllcEx());
                    }

                }
            }
        }


        BigDecimal bp = BigDecimal.ZERO;
        BigDecimal tc = BigDecimal.ZERO;
        //rescueProject将所有key含剥皮的数据进行合并，并赋值给escueProject
        for (Map.Entry<String, BigDecimal> entry : rescueProject.entrySet()) {
            if (entry.getKey().contains("剥皮")) {
                bp = bp.add(entry.getValue());
            }
            if (entry.getKey().contains("探伤")) {
                tc = tc.add(entry.getValue());
            }
        }

        //移除剥皮探伤数据 通过迭代器的方式
        Iterator<Map.Entry<String, BigDecimal>> iterator = rescueProject.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, BigDecimal> item = iterator.next();
            if (item.getKey().contains("剥皮") || item.getKey().contains("探伤")) {
                iterator.remove();
            }
        }

        rescueProject.put("剥皮", bp);
        rescueProject.put("探伤", tc);




        



        WaterfallChart waterfallChart = new WaterfallChart();
        waterfallChart.setRescueProject(rescueProject);

        return waterfallChart;
    }

    @Override
    public FactoryRejectionChart getFactoryRejectionChartDetail(QualityCostDetail qualityCostDetail) {
        qualityCostDetail.setTypeCode("C2");
        List<QualityCostDetail> qualityCostDetails = dashboardMapper.selectQualityCostDetailList(qualityCostDetail);

        HashMap<String, BigDecimal> factoryRejectionMap = new HashMap<>();

        List<String> sortOrder = Arrays.asList(
                "试验检测所", "一炼", "一轧", "二炼", "二轧大棒",
                "二轧小棒", "三轧钢", "高线", "杨市棒扁", "杨市盘卷",
                "银亮材", "线材深加工", "特板厂炼钢", "厚板",
                "中板", "钢板深加工", "三炼钢","公司"
        );

        for (String costCenterName : sortOrder) {
            factoryRejectionMap.put(costCenterName,BigDecimal.ZERO);
        }


        for (QualityCostDetail costDetail : qualityCostDetails) {
            if (qualityCostDetail.getContainType() == 1) {
                factoryRejectionMap.put(costDetail.getCostCenterCname(), costDetail.getCostEx());
            } else {
                factoryRejectionMap.put(costDetail.getCostCenterCname(), costDetail.getAllcEx());
            }
        }

        FactoryRejectionChart factoryRejectionChart = new FactoryRejectionChart();
        factoryRejectionChart.setFactoryRejectionMap(factoryRejectionMap);

        return factoryRejectionChart;
    }

    @Override
    public FactoryScrapChart getFactoryScrapChartDetail(QualityCostDetail qualityCostDetail) {
        qualityCostDetail.setTypeCode("C1");
        List<QualityCostDetail> qualityCostDetails = dashboardMapper.selectQualityCostDetailList(qualityCostDetail);

        HashMap<String, BigDecimal> factoryScrapMap = new HashMap<>();

        List<String> sortOrder = Arrays.asList(
                "试验检测所", "一炼", "一轧", "二炼", "二轧大棒",
                "二轧小棒", "三轧钢", "高线", "杨市棒扁", "杨市盘卷",
                "银亮材", "线材深加工", "特板厂炼钢", "厚板",
                "中板", "钢板深加工", "三炼钢","公司"
        );

        for (String costCenterName : sortOrder) {
            factoryScrapMap.put(costCenterName,BigDecimal.ZERO);
        }


        for (QualityCostDetail costDetail : qualityCostDetails) {
            if (qualityCostDetail.getContainType() == 1) {
                factoryScrapMap.put(costDetail.getCostCenterCname(), costDetail.getCostEx());
            } else {
                factoryScrapMap.put(costDetail.getCostCenterCname(), costDetail.getAllcEx());
            }
        }

        FactoryScrapChart factoryScrapChart = new FactoryScrapChart();
        factoryScrapChart.setFactoryScrapMap(factoryScrapMap);

        return factoryScrapChart;
    }

    @Override
    public FactoryContractChart getFactoryContractChartDetail(QualityCostDetail qualityCostDetail) {
        qualityCostDetail.setTypeCode("C3");
        List<QualityCostDetail> qualityCostDetails = dashboardMapper.selectQualityCostDetailList(qualityCostDetail);

        HashMap<String, BigDecimal> factoryContractMap = new HashMap<>();

        List<String> sortOrder = Arrays.asList(
                "试验检测所", "一炼", "一轧", "二炼", "二轧大棒",
                "二轧小棒", "三轧钢", "高线", "杨市棒扁", "杨市盘卷",
                "银亮材", "线材深加工", "特板厂炼钢", "厚板",
                "中板", "钢板深加工", "三炼钢","公司"
        );

        for (String costCenterName : sortOrder) {
            factoryContractMap.put(costCenterName,BigDecimal.ZERO);
        }


        for (QualityCostDetail costDetail : qualityCostDetails) {
            if (qualityCostDetail.getContainType() == 1) {
                factoryContractMap.put(costDetail.getCostCenterCname(), costDetail.getCostEx());
            } else {
                factoryContractMap.put(costDetail.getCostCenterCname(), costDetail.getAllcEx());
            }
        }

        FactoryContractChart factoryContractChart = new FactoryContractChart();
        factoryContractChart.setFactoryContractMap(factoryContractMap);

        return factoryContractChart;

    }

    @Override
    public FactoryReturnChart getFactoryReturnChartDetail(QualityCostDetail qualityCostDetail) {
        qualityCostDetail.setTypeCode("D1");
        List<QualityCostDetail> qualityCostDetails = dashboardMapper.selectQualityCostDetailList(qualityCostDetail);

        HashMap<String, BigDecimal> factoryReturnMap = new HashMap<>();

        List<String> sortOrder = Arrays.asList(
                "试验检测所", "一炼", "一轧", "二炼", "二轧大棒",
                "二轧小棒", "三轧钢", "高线", "杨市棒扁", "杨市盘卷",
                "银亮材", "线材深加工", "特板厂炼钢", "厚板",
                "中板", "钢板深加工", "三炼钢","公司"
        );

        for (String costCenterName : sortOrder) {
            factoryReturnMap.put(costCenterName,BigDecimal.ZERO);
        }


        for (QualityCostDetail costDetail : qualityCostDetails) {
            if (qualityCostDetail.getContainType() == 1) {
                factoryReturnMap.put(costDetail.getCostCenterCname(), costDetail.getCostEx());
            } else {
                factoryReturnMap.put(costDetail.getCostCenterCname(), costDetail.getAllcEx());
            }
        }

        FactoryReturnChart factoryReturnChart = new FactoryReturnChart();
        factoryReturnChart.setFactoryReturnMap(factoryReturnMap);

        return factoryReturnChart;
    }

    @Override
    public ScrapLossChart getScrapLossChartDetailsDetail(QualityCostDetail qualityCostDetail) {
        List<QualityCostDetail> qualityCostDetails = dashboardMapper.selectQualityCostDetailList(qualityCostDetail);

        HashMap<String, BigDecimal> scrapLossMap = new HashMap<>();

        List<CpTdDGxJzgyde> cpTdDGxJzgydes = cpTdDGxJzgydeMapper.selectCpTdDGxJzgydeList(new CpTdDGxJzgyde());

        HashMap<String, String> convertMap = new HashMap<>();
        for (CpTdDGxJzgyde cpTdDGxJzgyde : cpTdDGxJzgydes) {
            convertMap.put(cpTdDGxJzgyde.getFjzgyMc(), cpTdDGxJzgyde.getFjzgy());
        }

        if (qualityCostDetail.getContainType() == 1) {

            for (QualityCostDetail costDetail : qualityCostDetails) {
                if (this.isValidC5String(costDetail.getTypeCode())) {
                    if (convertMap.get(costDetail.getTypeName()) == null || convertMap.get(costDetail.getTypeName()).isEmpty()) {
                        scrapLossMap.put(costDetail.getTypeName(), costDetail.getCostEx());
                    } else {
                        scrapLossMap.put(convertMap.get(costDetail.getTypeName()), costDetail.getCostEx());
                    }

                }
            }
        } else {
            for (QualityCostDetail costDetail : qualityCostDetails) {
                if (this.isValidC5String(costDetail.getTypeCode())) {
                    if (convertMap.get(costDetail.getTypeName()) == null || convertMap.get(costDetail.getTypeName()).isEmpty()) {
                        scrapLossMap.put(costDetail.getTypeName(), costDetail.getAllcEx());
                    } else {
                        scrapLossMap.put(convertMap.get(costDetail.getTypeName()), costDetail.getAllcEx());
                    }

                }
            }
        }

        ScrapLossChart scrapLossChart = new ScrapLossChart();
        scrapLossChart.setScrapLossMap(scrapLossMap);

        return scrapLossChart;
    }

    @Override
    public QualityObjectionLossChart getQualityObjectionLossDetail(QualityCostDetail qualityCostDetail) {
        List<QualityCostDetail> qualityCostDetails = dashboardMapper.selectQualityCostDetailList(qualityCostDetail);

        HashMap<String, BigDecimal> qualityObjectionLossMap = new HashMap<>();

        List<CpTdDGxJzgyde> cpTdDGxJzgydes = cpTdDGxJzgydeMapper.selectCpTdDGxJzgydeList(new CpTdDGxJzgyde());

        HashMap<String, String> convertMap = new HashMap<>();
        for (CpTdDGxJzgyde cpTdDGxJzgyde : cpTdDGxJzgydes) {
            convertMap.put(cpTdDGxJzgyde.getFjzgyMc(), cpTdDGxJzgyde.getFjzgy());
        }

        String C4 = "C4";
        int length = C4.length();

        if (qualityCostDetail.getContainType() == 1) {
            for (QualityCostDetail costDetail : qualityCostDetails) {
                if (this.isValidD5String(costDetail.getTypeCode())) {
                    if (convertMap.get(costDetail.getTypeName()) == null || convertMap.get(costDetail.getTypeName()).isEmpty()) {
                        qualityObjectionLossMap.put(costDetail.getTypeName(), costDetail.getCostEx());
                    } else {
                        qualityObjectionLossMap.put(convertMap.get(costDetail.getTypeName()), costDetail.getCostEx());
                    }

                }
            }
        } else {
            for (QualityCostDetail costDetail : qualityCostDetails) {
                if (this.isValidD5String(costDetail.getTypeCode())) {
                    if (convertMap.get(costDetail.getTypeName()) == null || convertMap.get(costDetail.getTypeName()).isEmpty()) {
                        qualityObjectionLossMap.put(costDetail.getTypeName(), costDetail.getAllcEx());
                    } else {
                        qualityObjectionLossMap.put(convertMap.get(costDetail.getTypeName()), costDetail.getAllcEx());
                    }

                }
            }
        }


        QualityObjectionLossChart qualityObjectionLossChart = new QualityObjectionLossChart();
        qualityObjectionLossChart.setQualityObjectionLossMap(qualityObjectionLossMap);

        return qualityObjectionLossChart;
    }



    private boolean isValidD5String(String str) {
        // 空值检查
        if (str == null) {
            return false;
        }

        // 必须恰好 5 个字符
        if (str.length() <= 2) {
            return false;
        }

        // 必须以大写 "D5" 开头
        if (!str.startsWith("D5")) {
            return false;
        }

        return true;
    }

    private boolean isValidC5String(String str) {
        // 空值检查
        if (str == null) {
            return false;
        }

        // 必须恰好 5 个字符
        if (str.length() <= 2) {
            return false;
        }

        // 必须以大写 "C5" 开头
        if (!str.startsWith("C5")) {
            return false;
        }

        return true;
    }

    private boolean isValidC4String(String str) {
        // 空值检查
        if (str == null) {
            return false;
        }

        // 必须恰好 5 个字符
        if (str.length() <= 2) {
            return false;
        }

        // 必须以大写 "C4" 开头
        if (!str.startsWith("C4")) {
            return false;
        }

        return true;
    }

    @Override
    public QualityCostDetail getQualityCostDetail(QualityCostDetail qualityCostDetail) {
        List<QualityCostDetail> qualityCostDetails = dashboardMapper.selectQualityCostDetailList(qualityCostDetail);
        if (qualityCostDetails.size() > 0 )  {
            for (QualityCostDetail costDetail : qualityCostDetails) {
                if (costDetail.getTypeCode().equals("Z")) {
                    return costDetail;
                }
            }
        }
        return null;
    }

    @Override
    public ExternalCostDetailChart getExternalCostDetail(QualityCostDetail qualityCostDetail) {
        HashMap<String, BigDecimal> returnLoss = new HashMap<>();
        HashMap<String, BigDecimal> customerClaimCost = new HashMap<>();
        HashMap<String, BigDecimal> qualityObjectionFeeCost = new HashMap<>();
        HashMap<String, BigDecimal> qualityObjectionTravelCost = new HashMap<>();

        List<QualityCostDetail> qualityCostDetails = dashboardMapper.selectQualityCostDetailList(qualityCostDetail);

        if (qualityCostDetail.getContainType() == 1) {
            for (QualityCostDetail costDetail : qualityCostDetails) {
                if (costDetail.getTypeCode().equals("D1")) {
                    returnLoss.put(costDetail.getTypeName(), costDetail.getCostEx());
                } else if (costDetail.getTypeCode().equals("D2")) {
                    customerClaimCost.put(costDetail.getTypeName(), costDetail.getCostEx());
                } else if (costDetail.getTypeCode().equals("D3")) {
                    qualityObjectionFeeCost.put(costDetail.getTypeName(), costDetail.getCostEx());
                } else if (costDetail.getTypeCode().equals("D4")) {
                    qualityObjectionTravelCost.put(costDetail.getTypeName(), costDetail.getCostEx());
                }
            }
        } else {
            for (QualityCostDetail costDetail : qualityCostDetails) {
                if (costDetail.getTypeCode().equals("D1")) {
                    returnLoss.put(costDetail.getTypeName(), costDetail.getAllcEx());
                } else if (costDetail.getTypeCode().equals("D2")) {
                    customerClaimCost.put(costDetail.getTypeName(), costDetail.getAllcEx());
                }else if (costDetail.getTypeCode().equals("D3")) {
                    qualityObjectionFeeCost.put(costDetail.getTypeName(), costDetail.getAllcEx());
                }else if (costDetail.getTypeCode().equals("D4")) {
                    qualityObjectionTravelCost.put(costDetail.getTypeName(), costDetail.getAllcEx());
                }
            }
        }



        ExternalCostDetailChart externalCostDetailChart = new ExternalCostDetailChart();
        externalCostDetailChart.setReturnLoss(returnLoss);
        externalCostDetailChart.setCustomerClaimCost(customerClaimCost);
        externalCostDetailChart.setQualityObjectionFeeCost(qualityObjectionFeeCost);
        externalCostDetailChart.setQualityObjectionTravelCost(qualityObjectionTravelCost);

        return externalCostDetailChart;
    }

    @Override
    public InternalCostDetailChart getInternalCostDetail(QualityCostDetail qualityCostDetail) {
        HashMap<String, BigDecimal> scrapLoss = new HashMap<>();
        HashMap<String, BigDecimal> revisionLoss = new HashMap<>();
        HashMap<String, BigDecimal> contractionLoss = new HashMap<>();
        HashMap<String, BigDecimal> rescueCost = new HashMap<>();

        List<QualityCostDetail> qualityCostDetails = dashboardMapper.selectQualityCostDetailList(qualityCostDetail);

        if (qualityCostDetail.getContainType() == 1) {
            for (QualityCostDetail costDetail : qualityCostDetails) {
                if (costDetail.getTypeCode().equals("C1")) {
                    scrapLoss.put(costDetail.getTypeName(), costDetail.getCostEx());
                } else if (costDetail.getTypeCode().equals("C2")) {
                    revisionLoss.put(costDetail.getTypeName(), costDetail.getCostEx());
                } else if (costDetail.getTypeCode().equals("C3")) {
                    contractionLoss.put(costDetail.getTypeName(), costDetail.getCostEx());
                } else if (costDetail.getTypeCode().equals("C4")) {
                    rescueCost.put(costDetail.getTypeName(), costDetail.getCostEx());
                }
            }
        } else {
            for (QualityCostDetail costDetail : qualityCostDetails) {
                if (costDetail.getTypeCode().equals("C1")) {
                    scrapLoss.put(costDetail.getTypeName(), costDetail.getAllcEx());
                } else if (costDetail.getTypeCode().equals("C2")) {
                    revisionLoss.put(costDetail.getTypeName(), costDetail.getAllcEx());
                }else if (costDetail.getTypeCode().equals("C3")) {
                    contractionLoss.put(costDetail.getTypeName(), costDetail.getAllcEx());
                }else if (costDetail.getTypeCode().equals("C4")) {
                    rescueCost.put(costDetail.getTypeName(), costDetail.getAllcEx());
                }
            }
        }


        InternalCostDetailChart internalCostDetailChart = new InternalCostDetailChart();
        internalCostDetailChart.setScrapLoss(scrapLoss);
        internalCostDetailChart.setRevisionLoss(revisionLoss);
        internalCostDetailChart.setContractionLoss(contractionLoss);
        internalCostDetailChart.setRescueCost(rescueCost);

        return internalCostDetailChart;
    }


}
