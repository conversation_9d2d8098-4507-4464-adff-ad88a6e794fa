<template>
  <el-dialog
    title="服务项目查询"
    :visible.sync="visible"
    width="900px"
    append-to-body
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px">
      <el-form-item label="服务编号" prop="serviceNo">
        <el-input
          v-model="queryParams.serviceNo"
          placeholder="请输入服务编号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="服务名称" prop="serviceName">
        <el-input
          v-model="queryParams.serviceName"
          placeholder="请输入服务名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item >
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 服务项目列表 -->
    <el-table 
      v-loading="loading" 
      :data="serviceList" 
      @row-click="handleRowClick"
      @row-dblclick="handleRowDoubleClick"
      highlight-current-row
      style="cursor: pointer;"
    >
      <el-table-column label="服务编号" align="center" prop="serviceNo" width="150" />
      <el-table-column label="服务名称" align="center" prop="serviceName" />
      <el-table-column label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleSelect(scope.row)"
          >选择</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { listService } from "@/api/suppPunishment/service";

export default {
  name: "ServiceProjectDialog",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 弹窗显示状态
      visible: false,
      // 总条数
      total: 0,
      // 服务项目列表
      serviceList: [],
      // 当前选中行
      currentRow: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serviceNo: null,
        serviceName: null
      }
    };
  },
  methods: {
    /** 显示弹窗 */
    show() {
      this.visible = true;
      this.resetQuery();
      this.getList();
    },
    /** 关闭弹窗 */
    handleClose() {
      this.visible = false;
      this.reset();
    },
    /** 重置数据 */
    reset() {
      this.serviceList = [];
      this.currentRow = null;
      this.total = 0;
      this.loading = false;
    },
    /** 查询服务项目列表 */
    getList() {
      this.loading = true;
      listService(this.queryParams).then(response => {
        this.serviceList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        serviceNo: null,
        serviceName: null
      };
      this.handleQuery();
    },
    /** 行点击事件 */
    handleRowClick(row) {
      this.currentRow = row;
    },
    /** 行双击事件 */
    handleRowDoubleClick(row) {
      this.handleSelect(row);
    },
    /** 选择服务项目 */
    handleSelect(row) {
      this.$emit('select', {
        serviceNo: row.serviceNo,
        serviceName: row.serviceName
      });
      this.handleClose();
    }
  }
};
</script>

<style scoped>
.dialog-footer {
  text-align: center;
}

::v-deep .el-table tbody tr:hover > td {
  background-color: #f5f7fa !important;
}

::v-deep .el-table tbody tr.current-row > td {
  background-color: #ecf5ff !important;
}
</style>

