package com.ruoyi.app.qualityCost.controller;

import java.math.BigDecimal;
import java.util.List;

import com.ruoyi.app.qualityCost.domain.ContractDeviation;
import com.ruoyi.app.qualityCost.domain.QualityObjection;
import com.ruoyi.app.qualityCost.domain.ScrapDetail;
import com.ruoyi.app.qualityCost.service.IQualityObjectionService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;

import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 外部损失成本-质量异议退货损失Controller
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/qualityCost/qualityObjection")
public class QualityObjectionController extends BaseController
{
    @Autowired
    private IQualityObjectionService qualityObjectionService;

    /**
     * 查询外部损失成本-质量异议退货损失列表
     */

    @GetMapping("/list")
    public TableDataInfo list(QualityObjection qualityObjection)
    {
        startPage();
        List<QualityObjection> list = qualityObjectionService.selectQualityObjectionList(qualityObjection);
        return getDataTable(list);
    }

    /**
     * 导出外部损失成本-质量异议退货损失列表
     */

    @Log(title = "外部损失成本-质量异议退货损失", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(QualityObjection qualityObjection)
    {
        List<QualityObjection> list = qualityObjectionService.selectQualityObjectionList(qualityObjection);
        ExcelUtil<QualityObjection> util = new ExcelUtil<QualityObjection>(QualityObjection.class);
        return util.exportExcel(list, "qualityObjection");
    }

    /**
     * 获取外部损失成本-质量异议退货损失详细信息
     */

    @GetMapping(value = "/{recCreator}")
    public AjaxResult getInfo(@PathVariable("recCreator") String recCreator)
    {
        return AjaxResult.success(qualityObjectionService.selectQualityObjectionById(recCreator));
    }

    /**
     * 新增外部损失成本-质量异议退货损失
     */

    @Log(title = "外部损失成本-质量异议退货损失", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QualityObjection qualityObjection)
    {
        return toAjax(qualityObjectionService.insertQualityObjection(qualityObjection));
    }

    /**
     * 修改外部损失成本-质量异议退货损失
     */

    @Log(title = "外部损失成本-质量异议退货损失", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QualityObjection qualityObjection)
    {
        return toAjax(qualityObjectionService.updateQualityObjection(qualityObjection));
    }

    /**
     * 删除外部损失成本-质量异议退货损失
     */

    @Log(title = "外部损失成本-质量异议退货损失", businessType = BusinessType.DELETE)
	@DeleteMapping("/{recCreators}")
    public AjaxResult remove(@PathVariable String[] recCreators)
    {
        return toAjax(qualityObjectionService.deleteQualityObjectionByIds(recCreators));
    }

    @GetMapping("/listAll")
    public TableDataInfo listAll(QualityObjection qualityObjection)
    {

        List<QualityObjection> list = null;
        if (qualityObjection.getSearchMode().equals("模糊搜索")) {
            startPage();
            list = qualityObjectionService.selectQualityObjectionList(qualityObjection);
        } else if (qualityObjection.getSearchMode().equals("精确搜索")) {
            startPage();
            list = qualityObjectionService.selectQualityObjectionListByAccurateSearch(qualityObjection);
        }

        return getDataTable(list);
    }

    @GetMapping("/getSum")
    public AjaxResult getSum(QualityObjection qualityObjection)
    {
        List<QualityObjection> list = null;
        if (qualityObjection.getSearchMode().equals("模糊搜索")) {
            startPage();
            list = qualityObjectionService.selectQualityObjectionList(qualityObjection);
        } else if (qualityObjection.getSearchMode().equals("精确搜索")) {
            startPage();
            list = qualityObjectionService.selectQualityObjectionListByAccurateSearch(qualityObjection);
        }
        BigDecimal sumCostTon = BigDecimal.ZERO;
        BigDecimal sumCostPerTon = BigDecimal.ZERO;
        BigDecimal sumCostEx = BigDecimal.ZERO;
        for (QualityObjection item : list) {
            if (item.getCostTon() != null) {
                sumCostTon = sumCostTon.add(item.getCostTon());
            }
            if (item.getCostPerTon() != null) {
                sumCostPerTon = sumCostPerTon.add(item.getCostPerTon());
            }
            if (item.getCostEx() != null) {
                sumCostEx = sumCostEx.add(item.getCostEx());
            }
        }
        ContractDeviation sumObj = new ContractDeviation();
        sumObj.setCostTon(sumCostTon);
        sumObj.setCostPerTon(sumCostPerTon);
        sumObj.setCostEx(sumCostEx);

        return AjaxResult.success(sumObj);
    }

    @GetMapping("/getAllSum")
    public AjaxResult getAllSum(QualityObjection qualityObjection)
    {
        List<QualityObjection> list = null;
        if (qualityObjection.getSearchMode().equals("模糊搜索")) {
            list = qualityObjectionService.selectQualityObjectionList(qualityObjection);
        } else if (qualityObjection.getSearchMode().equals("精确搜索")) {
            list = qualityObjectionService.selectQualityObjectionListByAccurateSearch(qualityObjection);
        }
        BigDecimal sumCostTon = BigDecimal.ZERO;
        BigDecimal sumCostPerTon = BigDecimal.ZERO;
        BigDecimal sumCostEx = BigDecimal.ZERO;
        for (QualityObjection item : list) {
            if (item.getCostTon() != null) {
                sumCostTon = sumCostTon.add(item.getCostTon());
            }
            if (item.getCostPerTon() != null) {
                sumCostPerTon = sumCostPerTon.add(item.getCostPerTon());
            }
            if (item.getCostEx() != null) {
                sumCostEx = sumCostEx.add(item.getCostEx());
            }
        }
        QualityObjection sumObj = new QualityObjection();
        sumObj.setCostTon(sumCostTon);
        sumObj.setCostPerTon(sumCostPerTon);
        sumObj.setCostEx(sumCostEx);

        return AjaxResult.success(sumObj);
    }
}
