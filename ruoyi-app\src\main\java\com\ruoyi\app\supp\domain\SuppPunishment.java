package com.ruoyi.app.supp.domain;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 供应商处罚对象 supp_punishment
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public class SuppPunishment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录创建责任者 */
    private String recCreator;

    /** 记录创建时刻 */
    private String recCreateTime;

    /** 记录修改责任者 */
    private String recRevisor;

    /** 记录修改时刻 */
    private String recReviseTime;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "1=草稿,2=确认")
    private int stateId;

    /** 编号 */
    @Excel(name = "编号")
    private String serialNo;

    /** 申请部门 */
    @Excel(name = "申请部门")
    private String deptNo;

    /** 申请人 */
    private String userNo;

    @Excel(name = "填报人")
    private String userName;

    /** 执行人 */
    private String confirmNo;

    @Excel(name = "确认人")
    private String confirmName;

    /** 确认部门 */
    @Excel(name = "确认部门")
    private String companyCode;

    /** 供应商代码 */
    @Excel(name = "供应商代码")
    private String suppId;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String suppName;

    /** 供应类型*/
    @Excel(name = "供应类型", readConverterExp = "M=货物,S=服务,P=工程")
    private String suppType;

    /** 物料编码 */
    private String itemNo;

    /** 物料名称 */
    private String itemName;

    /** 处罚类型 01-质量 02-安全 03-清洁生产 04-合规运营 05-环保 06-治安 07-设备 */
    @Excel(name = "处罚类型",readConverterExp = "1=质量,2=安全,3=清洁生产,4=合规运营,5=环保,6=治安,7=设备")
    private String punishmentType;

    /** 事件发生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "事件发生时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date happenedTime;

    /** 确认时间 */
    private String confirmTime;

    /** 处罚执行时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "处罚执行时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date punishmentTime;

    /** 质量异议单号 */
    @Excel(name = "质量异议单号")
    private String qualityNo;

    /** 文件报批单号 */
    @Excel(name = "文件报批单号")
    private String fileNo;

    /** 巡检处罚单号 */
    @Excel(name = "巡检处罚单号")
    private String checkNo;

    /** 安管处罚单号 */
    @Excel(name = "安管处罚单号")
    private String securityNo;

    /** 制度名称 */
    @Excel(name = "制度名称")
    private String systemNo;

    /** 依据内容 */
    @Excel(name = "依据内容")
    private String basisContent;

    /** 处罚金额 */
    @Excel(name = "处罚金额(元)")
    private BigDecimal punishmentAmt;

    /** 是否降级 */
    @Excel(name = "降级",readConverterExp = "Y=是,N=否")
    private String reduce;

    /** 是否淘汰 */
    @Excel(name = "淘汰",readConverterExp = "Y=是,N=否")
    private String pass;

    /** 暂缓 */
    @Excel(name = "暂缓(月)")
    private int hold;

    /** 处罚事由 */
    @Excel(name = "处罚事由")
    private String punishmentReason;

    /** 发票号 */
    @Excel(name = "发票号")
    private String invoiceNo;

    /** 处罚号 */
    @Excel(name = "处罚单号")
    private String punishmentNo;

    /** $column.columnComment */
    private Long id;

    public String getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(String confirmTime) {
        this.confirmTime = confirmTime;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getRecCreator() {
        return recCreator;
    }

    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    public String getRecCreateTime() {
        return recCreateTime;
    }

    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    public String getRecReviseTime() {
        return recReviseTime;
    }

    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    public String getRecRevisor() {
        return recRevisor;
    }

    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }



    public int getStateId() {
        return stateId;
    }

    public void setStateId(int stateId) {
        this.stateId = stateId;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getConfirmNo() {
        return confirmNo;
    }

    public void setConfirmNo(String confirmNo) {
        this.confirmNo = confirmNo;
    }

    public String getConfirmName() {
        return confirmName;
    }

    public void setConfirmName(String confirmName) {
        this.confirmName = confirmName;
    }

    public String getDeptNo() {
        return deptNo;
    }

    public void setDeptNo(String deptNo) {
        this.deptNo = deptNo;
    }

    public String getSuppId() {
        return suppId;
    }

    public void setSuppId(String suppId) {
        this.suppId = suppId;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public String getSuppType() {
        return suppType;
    }

    public void setSuppType(String suppType) {
        this.suppType = suppType;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }


    public String getPunishmentType() {
        return punishmentType;
    }

    public void setPunishmentType(String punishmentType) {
        this.punishmentType = punishmentType;
    }

    public String getPunishmentReason() {
        return punishmentReason;
    }

    public void setPunishmentReason(String punishmentReason) {
        this.punishmentReason = punishmentReason;
    }

    public Date getHappenedTime() {
        return happenedTime;
    }

    public void setHappenedTime(Date happenedTime) {
        this.happenedTime = happenedTime;
    }

    public Date getPunishmentTime() {
        return punishmentTime;
    }

    public void setPunishmentTime(Date punishmentTime) {
        this.punishmentTime = punishmentTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getQualityNo() {
        return qualityNo;
    }

    public void setQualityNo(String qualityNo) {
        this.qualityNo = qualityNo;
    }

    public String getFileNo() {
        return fileNo;
    }

    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    public String getCheckNo() {
        return checkNo;
    }

    public void setCheckNo(String checkNo) {
        this.checkNo = checkNo;
    }

    public String getSecurityNo() {
        return securityNo;
    }

    public void setSecurityNo(String securityNo) {
        this.securityNo = securityNo;
    }

    public String getSystemNo() {
        return systemNo;
    }

    public void setSystemNo(String systemNo) {
        this.systemNo = systemNo;
    }

    public String getBasisContent() {
        return basisContent;
    }

    public void setBasisContent(String basisContent) {
        this.basisContent = basisContent;
    }

    public BigDecimal getPunishmentAmt() {
        return punishmentAmt;
    }

    public void setPunishmentAmt(BigDecimal punishmentAmt) {
        this.punishmentAmt = punishmentAmt;
    }

    public String getReduce() {
        return reduce;
    }

    public void setReduce(String reduce) {
        this.reduce = reduce;
    }

    public String getPass() {
        return pass;
    }

    public void setPass(String pass) {
        this.pass = pass;
    }

    public int getHold() {
        return hold;
    }

    public void setHold(int hold) {
        this.hold = hold;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getPunishmentNo() {
        return punishmentNo;
    }

    public void setPunishmentNo(String punishmentNo) {
        this.punishmentNo = punishmentNo;
    }
}

