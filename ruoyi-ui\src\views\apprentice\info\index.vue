<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="工号" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入工号" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="入职年份" prop="employYear">
        <el-date-picker
          v-model="queryParams.employYear"
          type="year"
          placeholder="请选择入职年份"
          value-format="yyyy"
          clearable
          size="small">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option label="正常" value="0" />
          <el-option label="结业" value="1" />
          <el-option label="离职" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button type="warning" icon="el-icon-download" size="mini" @click="refreshPermissions">权限刷新</el-button>
      </el-col>
      <el-button type="info" icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="infoList">
      <el-table-column label="入职年份" align="center" prop="employYear" />
      <el-table-column label="工号" align="center" prop="userName" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="年龄" align="center" prop="age" />
      <el-table-column label="学历" align="center" prop="education" />
      <el-table-column label="作业区/科室" align="center" prop="office" />
      <el-table-column label="岗位" align="center" prop="post" />
      <el-table-column label="以师带徒期限" align="center">
        <template slot-scope="scope">
          {{ formatDeadline(scope.row.deadline) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          {{ formatStatus(scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改以师带徒基本信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="工号" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入工号" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="出生日期" prop="birthday">
          <el-input v-model="form.birthday" placeholder="请输入出生日期yyyy-MM-dd" />
        </el-form-item>
        <el-form-item label="入职年份" prop="employYear">
          <el-date-picker
            v-model="form.employYear"
            type="year"
            placeholder="请选择入职年份"
            value-format="yyyy">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="学历" prop="education">
          <!-- <el-input v-model="form.education" placeholder="请输入学历" /> -->

          <el-select v-model="form.education" placeholder="请选择学历">
            <el-option v-for="item in educationOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="毕业院校及专业" prop="profession">
          <el-input v-model="form.profession" placeholder="请输入毕业院校及专业" />
        </el-form-item>
        <el-form-item label="作业区/科室" prop="office">
          <el-input v-model="form.office" placeholder="请输入作业区/科室" />
        </el-form-item>
        <el-form-item label="岗位" prop="post">
          <el-input v-model="form.post" placeholder="请输入岗位" />
        </el-form-item>
        <el-form-item label="以师带徒期限" prop="deadline">
          <!-- <el-input v-model="form.deadline" placeholder="请输入以师带徒期限" /> -->
          <el-select v-model="form.deadline" placeholder="请选择期限">
            <el-option v-for="item in deadlineOptions" :key="item.dictValue" :label="item.dictLabel"
              :value="item.dictValue">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班组评价人" prop="teamEvaluateUser">
          <el-input v-model="form.teamEvaluateUser" placeholder="请输入班组评价人" />
        </el-form-item>
        <el-form-item label="工作导师评价人" prop="supervisorEvaluateUser">
          <el-input v-model="form.supervisorEvaluateUser" placeholder="请输入工作导师评价人" />
        </el-form-item>
        <el-form-item label="人力资源部评价人" prop="hrEvaluateUser">
          <el-input v-model="form.hrEvaluateUser" placeholder="请输入人力资源部评价人" />
        </el-form-item>
        <el-form-item label="企业导师评价人" prop="leaderEvaluateUser">
          <el-input v-model="form.leaderEvaluateUser" placeholder="请输入企业导师评价人" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据
          <el-link type="info" style="font-size:12px" @click="importTemplate">下载模板</el-link>
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInfo, getInfo, delInfo, addInfo, updateInfo, exportInfo, importTemplate, refreshPermissions } from "@/api/apprentice/info";
import { getToken } from "@/utils/auth";
export default {
  name: "Info",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 以师带徒基本信息表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: null,
        name: null,
        status: "0",
        employYear: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        // name: [
        //   { required: true, message: "姓名不能为空", trigger: "blur" }
        // ],
      },

      // 学历选择项
      educationOptions: [{
        value: '专科',
        label: '专科'
      }, {
        value: '本科',
        label: '本科'
      }, {
        value: '硕士',
        label: '硕士'
      }, {
        value: '博士',
        label: '博士'
      },],
      // 期限选择项
      deadlineOptions: [],
      // 状态选择项
      statusOptions: [
        { label: '正常', value: '0' },
        { label: '结业', value: '1' },
        { label: '离职', value: '2' }
      ],
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: true,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/web/apprentice/info/importData",
      },
    };
  },
  created() {
    this.getDicts('apprentice_record_deadline').then((response) => {
      this.deadlineOptions = response.data
    })
    this.getList();
  },
  methods: {
    /** 查询以师带徒基本信息列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then(response => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userName: "",
        name: "",
        age: null,
        education: "",
        profession: "",
        office: "",
        post: "",
        deadline: null,
        employYear: "",
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: "",
        status: "0",
        teamEvaluateUser: "",
        supervisorEvaluateUser: "",
        hrEvaluateUser: "",
        leaderEvaluateUser: ""
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加以师带徒基本信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改以师带徒基本信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      if(this.form.userName!=null)
      {
        this.form.userName=this.form.userName.trim();
      }
      // 处理评价人字段，确保不为null且去除空格
      this.form.teamEvaluateUser = this.form.teamEvaluateUser ? this.form.teamEvaluateUser.trim() : "";
      this.form.supervisorEvaluateUser = this.form.supervisorEvaluateUser ? this.form.supervisorEvaluateUser.trim() : "";
      this.form.hrEvaluateUser = this.form.hrEvaluateUser ? this.form.hrEvaluateUser.trim() : "";
      this.form.leaderEvaluateUser = this.form.leaderEvaluateUser ? this.form.leaderEvaluateUser.trim() : "";
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateInfo(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除以师带徒基本信息编号为"' + ids + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return delInfo(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      })
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '事件导入'
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then((response) => {
        this.download(response.msg)
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      // this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.download(response.msg);
      this.handleQuery();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有以师带徒基本信息数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return exportInfo(queryParams);
      }).then(response => {
        this.download(response.msg);
      })
    },
    refreshPermissions() {
      refreshPermissions().then(response => {
        this.msgSuccess("权限刷新成功");
      });
    },
    // 格式化期限
    formatDeadline(deadline) {
      let result = "";
      this.deadlineOptions.forEach(item => {
        if (item.dictValue == deadline) {
          result = item.dictLabel;
        }
      })
      return result;
    },
    // 格式化状态
    formatStatus(status) {
      let result = "";
      this.statusOptions.forEach(item => {
        if (item.value == status) {
          result = item.label;
        }
      })
      return result;
    },
  }
};
</script>