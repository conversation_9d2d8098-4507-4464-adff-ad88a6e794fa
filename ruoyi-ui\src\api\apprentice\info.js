import request from '@/utils/request'

// 查询以师带徒基本信息列表
export function listInfo(query) {
  return request({
    url: '/web/apprentice/info/list',
    method: 'get',
    params: query
  })
}

// 查询以师带徒基本信息详细
export function getInfo(id) {
  return request({
    url: '/web/apprentice/info/' + id,
    method: 'get'
  })
}

// 新增以师带徒基本信息
export function addInfo(data) {
  return request({
    url: '/web/apprentice/info',
    method: 'post',
    data: data
  })
}

// 修改以师带徒基本信息
export function updateInfo(data) {
  return request({
    url: '/web/apprentice/info',
    method: 'put',
    data: data
  })
}

// 删除以师带徒基本信息
export function delInfo(id) {
  return request({
    url: '/web/apprentice/info/' + id,
    method: 'delete'
  })
}

// 导出以师带徒基本信息
export function exportInfo(query) {
  return request({
    url: '/web/apprentice/info/export',
    method: 'get',
    params: query
  })
}

// 导出以师带徒基本信息
export function importTemplate(query) {
  return request({
    url: '/web/apprentice/info/importTemplate',
    method: 'get',
    params: query
  })
}

// 跟新权限
export function refreshPermissions() {
  return request({
    url: '/web/apprentice/info/refreshPermissions',
    method: 'put'
  })
}

// 按入职年份导出月度跟踪记录
export function exportByEmployYear(employYear, queryParams) {
  // 如果传递了查询参数，使用查询参数；否则使用employYear
  const params = queryParams || { employYear };

  return request({
    url: '/web/apprentice/info/exportByEmployYear',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}