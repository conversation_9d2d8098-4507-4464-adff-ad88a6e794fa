<template>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
        <el-row>
          <el-form-item label="考核年月" prop="assessDate">
            <el-date-picker
              v-model="queryParams.assessDate"
              type="month"
              value-format="yyyy-M"
              format="yyyy 年 M 月"
              placeholder="选择考核年月"
              :clearable="false">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入姓名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="部门" prop="deptId">
            <el-select v-model="queryParams.deptId" placeholder="请选择部门">
              <el-option
                v-for="item in deptOptions"
                :key="item.deptId"
                :label="item.deptName"
                :value="item.deptId"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>

      
      <!-- 待评分列表 -->
      <el-card class="box-card" style="margin-bottom: 20px;">
        <div slot="header" class="clearfix">
          <span style="font-size: 16px; font-weight: bold; color: #409EFF;">
            <i class="el-icon-s-order"></i>
            {{ toCheckLabel }}
          </span>
        </div>
        
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              @click="handleBatchQuickScore"
            >批量快速评分</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
          <el-table v-loading="loading" :data="listToCheck" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="工号" align="center" prop="workNo" width="120"/>
            <el-table-column label="姓名" align="center" prop="name" width="120"/>
            <el-table-column label="部门" align="center" prop="deptName" ></el-table-column>
            <el-table-column label="自评分" align="center" prop="selfScore"></el-table-column>
            <el-table-column label="部门领导评分" align="center" prop="deptScore">
              <template slot-scope="scope">
                <span v-if="scope.row.status == '1'" style="font-weight: bold; color: #409EFF;">{{ getDeptScore(scope.row) }}</span>
                <span v-else-if="scope.row.deptScore">{{ scope.row.deptScore }}</span>
                <span v-else></span>
              </template>
            </el-table-column>
            <el-table-column v-if="showBusiness" label="事业部评分" align="center" prop="businessScore">
              <template slot-scope="scope">
                <span v-if="scope.row.status == '2'" style="font-weight: bold; color: #409EFF;">{{ getBusinessScore(scope.row) }}</span>
                <span v-else-if="scope.row.businessScore">{{ scope.row.businessScore }}</span>
                <span v-else></span>
              </template>
            </el-table-column>
            <el-table-column label="加减分" align="center" width="120">
              <template slot-scope="scope">
                <el-input-number 
                  v-if="scope.row.status == '1' || scope.row.status == '2'"
                  v-model="scope.row.quickAddScore" 
                  :min="-100" 
                  :max="100" 
                  size="mini"
                  :precision="1"
                  style="width: 100px"
                  placeholder="加减分">
                </el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="加减分原因" align="center">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.quickReason"
                  type="textarea"
                  :autosize="{ minRows: 1, maxRows: 4}"
                  size="mini"
                  style="width: 150px"
                  placeholder="请输入加减分原因">
                </el-input>
              </template>
            </el-table-column>
            <!-- <el-table-column label="运改组织部评分" align="center" prop="organizationScore">
                <el-input-number 
                  v-if="scope.row.status == '3'"
                  v-model="scope.row.quickScore" 
                  :min="0" 
                  :max="100" 
                  size="mini"
                  style="width: 120px"
                  placeholder="请输入分数">
                </el-input-number>
                <span v-else-if="scope.row.businessScore">{{ businessScore }}</span>
                <span v-else></span>
            </el-table-column> -->
            <!-- <el-table-column label="快速评分" align="center" width="280">
              <template slot-scope="scope">
                <el-input-number 
                  v-model="scope.row.quickScore" 
                  :min="0" 
                  :max="100" 
                  size="mini"
                  style="width: 120px"
                  placeholder="请输入分数">
                </el-input-number>
                <el-button
                  size="mini"
                  type="primary"
                  @click="handleQuickSubmit(scope.row)"
                  :loading="scope.row.submitting"
                  style="margin-left: 10px">
                  提交评分
                </el-button>
              </template>
            </el-table-column> -->
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleCheckDetail(scope.row)"
                >详细评分</el-button>
                <el-button
                  v-if="canReturn(scope.row)"
                  size="mini"
                  type="text"
                  icon="el-icon-back"
                  @click="handleReturn(scope.row)"
                  style="color: #E6A23C;"
                >退回</el-button>
              </template>
            </el-table-column>
          </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>

      <!-- 评分记录 -->
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span style="font-size: 16px; font-weight: bold; color: #67C23A;">
            <i class="el-icon-document"></i>
            评分记录({{ checkedTotal }})
          </span>
        </div>
        
        <el-table v-loading="loading" :data="listChecked">
          <el-table-column label="工号" align="center" prop="workNo" width="120"/>
          <el-table-column label="姓名" align="center" prop="name" width="120"/>
          <el-table-column label="部门" align="center" prop="deptName" />
          <el-table-column label="职务" align="center" prop="job" width="150"/>
          <el-table-column label="评分类型" align="center" prop="type" >
            <template slot-scope="scope">
              <el-tag v-if="scope.row.type == '1'" type="primary" size="small">部门领导评分</el-tag>
              <el-tag v-if="scope.row.type == '2'" type="warning" size="small">事业部领导评分</el-tag>
              <el-tag v-if="scope.row.type == '3'" type="success" size="small">运改组织部审核</el-tag>
              <el-tag v-if="scope.row.type == '4'" type="info" size="small">条线领导评分</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="评分时间" align="center" prop="createTime" width="160"/>
          <el-table-column label="评分" align="center" prop="score" width="100"/>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleCheckedDetail(scope.row)"
              >查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <pagination
          v-show="checkedTotal>0"
          :total="checkedTotal"
          :page.sync="checkedQueryParams.pageNum"
          :limit.sync="checkedQueryParams.pageSize"
          @pagination="getCheckedList"
          style="margin-top: 20px;"
        />
      </el-card>

          <el-dialog
      :visible.sync="open"
      fullscreen
      class="assessment-detail-dialog">
      <div class="detail-container">
        <div class="detail-header">
          <h2 style="text-align: center; color: #303133; margin-bottom: 20px;">
            <i class="el-icon-document"></i>
            月度业绩考核表
          </h2>
          <el-card shadow="never" style="margin-bottom: 20px;">
            <el-descriptions class="margin-top" :column="3" border>
              <el-descriptions-item>
                <template slot="label">
                  <i class="el-icon-user"></i> 姓名
                </template>
                {{ checkInfo.name }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  <i class="el-icon-office-building"></i> 部门
                </template>
                {{ checkInfo.deptName }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  <i class="el-icon-date"></i> 考核年月
                </template>
                {{ checkInfo.assessDate }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </div>
        
        <el-card shadow="never" class="assessment-table-card">
          <div slot="header" class="clearfix">
            <span style="font-size: 16px; font-weight: bold; color: #409EFF;">
              <i class="el-icon-s-data"></i>
              考核详情
            </span>
          </div>
          <el-table v-loading="loading" :data="checkInfo.list"
            :span-method="objectSpanMethod" border stripe>
            <el-table-column label="类型" align="center" prop="item" width="120"/>
            <el-table-column label="指标" align="center" prop="category" width="150"/>
            <el-table-column label="目标" align="center" prop="target" width="180"/>
            <el-table-column label="评分标准" align="center" prop="standard" />
            <el-table-column label="完成实绩（若扣分，写明原因）" align="center" prop="performance" />
            <el-table-column label="加减分" align="center" prop="dePoints" width="150" />
            <el-table-column label="加减分原因" align="center" prop="pointsReason" width="180" />
          </el-table>
        </el-card>
        
        <el-card shadow="never" class="signature-card" style="margin-top: 20px;">
          <div slot="header" class="clearfix">
            <span style="font-size: 16px; font-weight: bold; color: #67C23A;">
              <i class="el-icon-edit-outline"></i>
              评分记录
            </span>
          </div>
          <el-form size="small" :inline="false" label-width="200px" label-position="left">
            <!-- 自评分 -->
            <el-form-item>
              <template slot="label">
                <span style="color: #606266;">
                  自评分数 / 签名：
                </span>
              </template>
              <div class="signature-content">
                <span class="score-text">{{ checkInfo.selfScore }} 分</span>
                <span class="separator">/</span>
                <span class="signature-name">{{ checkInfo.name }}</span>
              </div>
            </el-form-item>
            
            <!-- 部门领导评分 -->
            <el-form-item v-if="checkInfo.deptScore && checkInfo.deptUserName">
              <template slot="label">
                <span style="color: #606266;">
                  部门领导评分 / 签名：
                </span>
              </template>
              <div class="signature-content">
                <span class="score-text">{{ checkInfo.deptScore }} 分</span>
                <span class="separator">/</span>
                <span class="signature-name">{{ checkInfo.deptUserName }}</span>
                <div v-if="checkInfo.deptScoreReason" class="reason-text">
                  <span class="reason-label">加减分理由：</span>
                  <span class="reason-content">{{ checkInfo.deptScoreReason }}</span>
                </div>
              </div>
            </el-form-item>
            
            <!-- 事业部领导评分 -->
            <el-form-item v-if="checkInfo.businessUserName && checkInfo.businessScore">
              <template slot="label">
                <span style="color: #606266;">
                  事业部领导评分 / 签名：
                </span>
              </template>
              <div class="signature-content">
                <span class="score-text">{{ checkInfo.businessScore }} 分</span>
                <span class="separator">/</span>
                <span class="signature-name">{{ checkInfo.businessUserName }}</span>
                <div v-if="checkInfo.businessScoreReason" class="reason-text">
                  <span class="reason-label">加减分理由：</span>
                  <span class="reason-content">{{ checkInfo.businessScoreReason }}</span>
                </div>
              </div>
            </el-form-item>
            
            <!-- 运改组织部评分 -->
            <el-form-item v-if="checkInfo.organizationScore && checkInfo.organizationUserName">
              <template slot="label">
                <span style="color: #606266;">
                  运改组织部评分 / 签名：
                </span>
              </template>
              <div class="signature-content">
                <span class="score-text">{{ checkInfo.organizationScore }} 分</span>
                <span class="separator">/</span>
                <span class="signature-name">{{ checkInfo.organizationUserName }}</span>
                <div v-if="checkInfo.organizationScoreReason" class="reason-text">
                  <span class="reason-label">加减分理由：</span>
                  <span class="reason-content">{{ checkInfo.organizationScoreReason }}</span>
                </div>
              </div>
            </el-form-item>
            
            <!-- 当前状态评分输入 -->
            <el-form-item v-if="checkInfo.status == '1'" label="加减分：">
              <el-input-number v-model="form.deptAddScore" :min="-100" :max="100" :precision="1" placeholder="请输入加减分" style="width: 150px;" />
            </el-form-item>
            <el-form-item v-if="checkInfo.status == '1'" label="部门领导评分：">
              <span style="font-weight: bold; color: #409EFF; font-size: 16px;">{{ getDeptScoreFromForm() }}分</span>
            </el-form-item>
            <el-form-item v-if="checkInfo.status == '1'" label="加减分理由：">
              <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4}" v-model="form.deptScoreReason" placeholder="请输入加减分理由" style="width: 400px;" />
            </el-form-item>
            
            <el-form-item v-if="checkInfo.status == '2'" label="加减分：">
              <el-input-number v-model="form.businessAddScore" :min="-100" :max="100" :precision="1" placeholder="请输入加减分" style="width: 150px;" />
            </el-form-item>
            <el-form-item v-if="checkInfo.status == '2'" label="事业部领导评分：">
              <span style="font-weight: bold; color: #409EFF; font-size: 16px;">{{ getBusinessScoreFromForm() }}分</span>
            </el-form-item>
            <el-form-item v-if="checkInfo.status == '2'" label="加减分理由：">
              <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4}" v-model="form.businessScoreReason" placeholder="请输入加减分理由" style="width: 400px;" />
            </el-form-item>
          </el-form>
        </el-card>
        
        <div class="dialog-footer" style="text-align: center; margin-top: 30px; padding: 20px;">
          <el-button type="primary" size="medium" @click="checkSubmit">
            <i class="el-icon-check"></i> 提 交
          </el-button>
          <el-button plain type="info" size="medium" @click="cancel">
            <i class="el-icon-close"></i> 返 回
          </el-button>
        </div>
      </div>
    </el-dialog>

      <!-- 批量快速评分对话框 -->
      <el-dialog :title="'批量快速评分确认'" :visible.sync="batchQuickScoreOpen" width="800px" append-to-body>
        <el-alert
          title="请确认以下人员的评分信息"
          type="warning"
          :closable="false"
          show-icon
          class="mb20"
        />
        <el-table :data="selectedRows" size="small" border>
          <el-table-column label="工号" align="center" prop="workNo" />
          <el-table-column label="姓名" align="center" prop="name" />
          <el-table-column label="部门" align="center" prop="deptName" />
          <el-table-column label="岗位" align="center" prop="job" />
          <el-table-column label="加减分" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.quickAddScore || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="最终评分" align="center" prop="quickScore">
            <template slot-scope="scope">
              <span v-if="scope.row.status == '1'" style="font-weight: bold; color: #409EFF;">{{ getDeptScore(scope.row) }}</span>
              <span v-else-if="scope.row.status == '2'" style="font-weight: bold; color: #409EFF;">{{ getBusinessScore(scope.row) }}</span>
              <span v-else :class="{'text-red': !scope.row.quickScore}">{{ scope.row.quickScore || '未填写' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="加减分理由" align="center" prop="quickReason">
            <template slot-scope="scope">
              <span>{{ scope.row.quickReason || '' }}</span>
            </template>
          </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitBatchQuickScore" :disabled="!canSubmitBatchScore">确 定</el-button>
          <el-button @click="cancelBatchQuickScore">取 消</el-button>
          </div>
      </el-dialog>

      <!-- 退回确认对话框 -->
      <el-dialog title="退回确认" :visible.sync="returnOpen" width="500px" append-to-body>
        <el-form ref="returnForm" :model="returnForm" :rules="returnRules" label-width="100px">
          <el-form-item label="姓名">
            <span>{{ returnForm.name }}</span>
          </el-form-item>
          <el-form-item label="当前状态">
            <el-tag :type="getStatusType(returnForm.currentStatus)">{{ formatStatus(returnForm.currentStatus) }}</el-tag>
          </el-form-item>
          <el-form-item label="退回到" v-if="returnForm.currentStatus === '2'">
            <el-radio-group v-model="returnForm.targetStatus">
              <el-radio label="0">自评</el-radio>
              <el-radio label="1">部门领导评分</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="退回到" v-else>
            <el-tag :type="getStatusType(returnForm.targetStatus)">{{ formatStatus(returnForm.targetStatus) }}</el-tag>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelReturn">取 消</el-button>
          <el-button type="primary" @click="confirmReturn">确 定</el-button>
        </div>
      </el-dialog>

    </div>
  </template>

  <script>
  import { listToCheck, listChecked, getInfo, check, batchQuickScore, rejectInfo } from "@/api/assess/self/info"
  import { getCheckDeptList } from "@/api/assess/self/user";

  export default {
    name: "SelfAssessCheck",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 显示搜索条件
        showSearch: true,
        // 显示列表事业部评分
        showBusiness:false,
        // 总条数
        total: 0,
        checkedTotal: 0,
        // 绩效考核-干部自评人员配置表格数据
        listToCheck: [],
        listChecked: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          workNo: null,
          name:null,
          deptId:null,
          assessDate:null
        },
        // 评分记录查询参数
        checkedQueryParams: {
          pageNum: 1,
          pageSize: 10,
          workNo: null,
          name:null,
          deptId:null,
          assessDate:null
        },
        // 表单参数
        form: {
          id:null,
          // 部门领导加减分
          deptAddScore:null,
          // 事业部加减分
          businessAddScore:null,
          // 部门领导评分理由
          deptScoreReason:null,
          // 事业部评分理由
          businessScoreReason:null,
        },
        // 表单校验
        rules: {
        },
        deptOptions:[],
        openCheck:false,
        checkInfo:{},
        // 合并单元格
        spanList:[],
        // 待评分标签
        toCheckLabel:"待评分(0)",
        // 快速评分提交状态
        submitting: false,
        // 选中数组
        multipleSelection: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 批量快速评分对话框显示状态
        quickScoreDialogVisible: false,
        // 批量快速评分表单参数
        batchQuickScoreForm: {
          score: undefined,
          ids: []
        },
        // 批量快速评分表单验证规则
        batchQuickScoreRules: {
          score: [
            { required: true, message: "评分不能为空", trigger: "blur" },
            { type: 'number', message: "评分必须为数字", trigger: "blur" }
          ]
        },
        // 批量快速评分对话框
        batchQuickScoreOpen: false,
        // 选中数组
        ids: [],
        // 选中的行数据
        selectedRows: [],
        // 退回对话框
        returnOpen: false,
        returnForm: {
          id: null,
          name: null,
          currentStatus: null,
          targetStatus: null
        },
        returnRules: {}
      };
    },
      computed: {
    // 是否可以提交批量评分（基础检查）
    canSubmitBatchScore() {
      if (this.selectedRows.length === 0) return false;
      
      // 基础检查：是否所有行都填写了加减分
      for (let row of this.selectedRows) {
        if (row.quickAddScore === null || row.quickAddScore === undefined) {
          return false;
        }
      }
      
      return true;
    }
  },
    created() {
      this.queryParams.assessDate = this.getDefaultAssessDate()
      this.checkedQueryParams.assessDate = this.getDefaultAssessDate()
      // this.getSelfAssessUser();
      this.getCheckDeptList();
      this.getList();
      this.getCheckedList();
    },
    methods: {

      // 获取默认考核日期
      getDefaultAssessDate() {
        const now = new Date();
        const currentDay = now.getDate();

        let targetDate;
        if (currentDay < 10) {
          // 当前日期小于10日，默认为上个月
          targetDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        } else {
          // 当前日期大于等于10日，默认为当月
          targetDate = new Date(now.getFullYear(), now.getMonth(), 1);
        }

        // 格式化为 YYYY-M 格式
        const year = targetDate.getFullYear();
        const month = targetDate.getMonth() + 1;
        return `${year}-${month}`;
      },

      // 获取部门信息
      getCheckDeptList(){
        getCheckDeptList().then(res => {
          console.log(res)
          if(res.code == 200){
            let deptOptions = [];
            res.data.forEach(item => {
              deptOptions.push({
                deptName:item.deptName,
                deptId:item.deptId
              })
            })
            this.deptOptions = deptOptions;
          }
        })
      },
      /** 查询绩效考核-干部自评待审核列表 */
      getList() {
        this.loading = true;
        listToCheck(this.queryParams).then(response => {
          this.listToCheck = response.rows;
          this.total = response.total;
          this.toCheckLabel = `待评分(${response.total})`
          this.loading = false;
          this.shouldBusinessDisplay();
        });
      },

      shouldBusinessDisplay(){
        this.showBusiness = this.listToCheck.some(row => row["status"] == '2')
      },
      
      /** 获取已审核列表 */
      getCheckedList(){
        this.loading = true;
        listChecked(this.checkedQueryParams).then(res => {
          this.listChecked = res.rows;
          this.checkedTotal = res.total;
          this.loading = false;
        })
      },

      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          id: null,
          deptAddScore: null,
          businessAddScore: null,
          deptScoreReason: null,
          businessScoreReason: null,
        };
        // this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.checkedQueryParams.pageNum = 1;
        // 同步搜索条件
        this.checkedQueryParams.name = this.queryParams.name;
        this.checkedQueryParams.deptId = this.queryParams.deptId;
        this.checkedQueryParams.assessDate = this.queryParams.assessDate;
        this.getCheckedList();
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },

      // 审批详情
      handleCheckDetail(row){
        getInfo({id:row.id}).then(res => {
          console.log(res);
          if(res.code == 200){
            this.checkInfo = res.data;
            let list = JSON.parse(res.data.content);
            this.handleSpanList(list);
            this.checkInfo.list = list;
          }
          this.open = true
        })
      },

      // 审批提交
      checkSubmit(){
        if(this.verify()){
          let point = this.getDeptScoreFromForm();
          if(this.checkInfo.status == '2') point = this.getBusinessScoreFromForm();
          this.$confirm('是否确认' + this.checkInfo.name + '评分为：' + point + '分', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.onCheck();
          }).catch(() => {

          });
        }
      },

      onCheck(){
        this.form.id = this.checkInfo.id;
        this.form.status = this.checkInfo.status;
        
        // 计算最终评分
        if(this.checkInfo.status == '1') {
          this.form.deptScore = this.getDeptScoreFromForm();
        }
        if(this.checkInfo.status == '2') {
          this.form.businessScore = this.getBusinessScoreFromForm();
        }
        
        check(this.form).then(res => {
          console.log(res)
          if(res.code == 200){
            this.$message({
              type: 'success',
              message: '提交成功!'
            });
            this.reset();
            this.open = false;
            this.getList();
            this.getCheckedList();
          }else{
            this.$message({
              type: 'warning',
              message: '操作失败，无权限或当前审批状态不匹配'
            });
          }
        })
      },

      // 数据验证
      verify(){
        if(this.checkInfo.status == '1' && this.form.deptAddScore === null){
          this.$message({
            type: 'warning',
            message: '请填写加减分'
          });
          return false;
        }
        if(this.checkInfo.status == '2' && this.form.businessAddScore === null){
          this.$message({
            type: 'warning',
            message: '请填写加减分'
          });
          return false;
        }
        if(this.checkInfo.status == '1' && this.form.deptAddScore !== 0 && !this.form.deptScoreReason){
          this.$message({
            type: 'warning',
            message: '有加减分时请填写加减分理由'
          });
          return false;
        } 
        if(this.checkInfo.status == '2' && this.form.businessAddScore !== 0 && !this.form.businessScoreReason){
          this.$message({
            type: 'warning',
            message: '有加减分时请填写加减分理由'
          });
          return false;
        }
        return true;
      },

      handleListChange(type){
        console.log(type)
      },
      // 处理列表
      handleSpanList(data){
        let spanList = [];
        let flag = 0;
        for(let i = 0; i < data.length; i++){
          // 相同考核项合并
          if(i == 0){
            spanList.push({
              rowspan: 1,
              colspan: 1
            })
          }else{
            if(data[i - 1].item == data[i].item){
              spanList.push({
                rowspan: 0,
                colspan: 0
              })
              spanList[flag].rowspan += 1;
            }else{
              spanList.push({
                rowspan: 1,
                colspan: 1
              })
              flag = i;
            }
          }
        }
        this.spanList = spanList;
      },

      // 合并单元格方法
      objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        // 第一列相同项合并
        if (columnIndex === 0) {
          return this.spanList[rowIndex];
        }
        // 类别无内容 合并
        if(columnIndex === 1){
          if(!row.category){
            return {
              rowspan: 0,
              colspan: 0
            }
          }
        }
        if(columnIndex === 2){
          if(!row.category){
            return {
              rowspan: 1,
              colspan: 2
            }
          }
        }
      },

      /** 快速评分提交 */
      handleQuickSubmit(row) {
        if (!row.quickScore) {
          this.$message.warning('请输入评分');
          return;
        }
        this.$confirm('确认提交该评分吗？', "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.$set(row, 'submitting', true);
          const data = {
            id: row.id,
            score: row.quickScore,
            type: row.type
          };
          check(data).then(response => {
            this.$message.success('评分提交成功');
            this.getList();
            this.getCheckedList();
          }).finally(() => {
            this.$set(row, 'submitting', false);
          });
        });
      },

      /** 选择条数改变 */
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id)
        this.selectedRows = selection
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },

      /** 批量快速评分按钮操作 */
      handleBatchQuickScore() {
        if (this.ids.length === 0) {
          this.$modal.msgError("请选择需要评分的数据");
          return;
        }
        
        // 验证评分一致性和理由必填
        const validationResult = this.validateBatchQuickScore();
        if (!validationResult.isValid) {
          this.$modal.msgError(validationResult.message);
          return;
        }

        this.batchQuickScoreOpen = true;
      },

      /** 取消批量快速评分操作 */
      cancelBatchQuickScore() {
        this.batchQuickScoreOpen = false;
      },

      /** 提交批量快速评分 */
      submitBatchQuickScore() {
        // 准备提交数据
        const submitData = this.selectedRows.map(row => {
          let finalScore;
          if (row.status == '1') {
            // 部门领导评分 = 自评分 + 加减分
            finalScore = this.getDeptScore(row);
          } else if (row.status == '2') {
            // 事业部评分 = 部门领导评分 + 加减分
            finalScore = this.getBusinessScore(row);
          }
          
          return {
            id: row.id,
            quickScore: parseFloat(finalScore),
            quickAddScore: row.quickAddScore,
            quickReason: row.quickReason
          };
        });

        this.$modal.confirm('是否确认提交选中人员的快速评分？').then(() => {
          return batchQuickScore(submitData);
        }).then(() => {
          this.$modal.msgSuccess("批量评分成功");
          this.batchQuickScoreOpen = false;
          this.getList();
          this.getCheckedList();
        }).catch(() => {});
      },

      /** 验证批量快速评分 */
      validateBatchQuickScore() {
        for (let i = 0; i < this.selectedRows.length; i++) {
          const row = this.selectedRows[i];
          
          // 检查是否填写了加减分（允许为0）
          if (row.quickAddScore === null || row.quickAddScore === undefined) {
            return {
              isValid: false,
              message: `第${i + 1}行 ${row.name} 请填写加减分`
            };
          }

          // 检查加减分不为0时是否填写了理由
          if (parseFloat(row.quickAddScore) !== 0 && !row.quickReason) {
            return {
              isValid: false,
              message: `第${i + 1}行 ${row.name} 有加减分时请填写加减分理由`
            };
          }
        }

        return { isValid: true };
      },

      /** 查看评分记录详情 */
      handleCheckedDetail(row) {
        getInfo({id: row.infoId}).then(res => {
          console.log(res);
          if(res.code == 200){
            this.checkInfo = res.data;
            let list = JSON.parse(res.data.content);
            this.handleSpanList(list);
            this.checkInfo.list = list;
            this.open = true;
          }
        }).catch(error => {
          this.$message.error('获取详情失败');
        });
      },

      // 计算部门领导评分（快速评分表格用）
      getDeptScore(row) {
        const selfScore = parseFloat(row.selfScore) || 0;
        const addScore = parseFloat(row.quickAddScore) || 0;
        const result = selfScore + addScore;
        // 确保评分在0-100范围内
        return Math.max(0, Math.min(100, result)).toFixed(1);
      },

      // 计算事业部评分（快速评分表格用）
      getBusinessScore(row) {
        const deptScore = parseFloat(row.deptScore) || 0;
        const addScore = parseFloat(row.quickAddScore) || 0;
        const result = deptScore + addScore;
        // 确保评分在0-100范围内
        return Math.max(0, Math.min(100, result)).toFixed(1);
      },

      // 计算部门领导评分（详细评分表单用）
      getDeptScoreFromForm() {
        const selfScore = parseFloat(this.checkInfo.selfScore) || 0;
        const addScore = parseFloat(this.form.deptAddScore) || 0;
        const result = selfScore + addScore;
        // 确保评分在0-100范围内
        return Math.max(0, Math.min(100, result)).toFixed(1);
      },

      // 计算事业部评分（详细评分表单用）
      getBusinessScoreFromForm() {
        const deptScore = parseFloat(this.checkInfo.deptScore) || 0;
        const addScore = parseFloat(this.form.businessAddScore) || 0;
        const result = deptScore + addScore;
        // 确保评分在0-100范围内
        return Math.max(0, Math.min(100, result)).toFixed(1);
      },

      /** 判断是否可以退回（待评分列表） */
      canReturn(row) {
        // 只有状态为1（部门领导评分）或2（事业部评分）的记录可以退回
        return row.status === '1' || row.status === '2';
      },



      /** 退回按钮操作（待评分列表） */
      handleReturn(row) {
        // 部门领导评分状态固定退回到自评状态
        if (row.status === '1') {
          this.returnForm = {
            id: row.id,
            name: row.name,
            currentStatus: row.status,
            targetStatus: '0'
          };
        }
        // 事业部评分状态可选择退回目标
        else if (row.status === '2') {
          this.returnForm = {
            id: row.id,
            name: row.name,
            currentStatus: row.status,
            targetStatus: '0' // 默认选择退回到自评
          };
        }
        else {
          this.$message.warning('当前状态无法退回');
          return;
        }

        this.returnOpen = true;
      },



      /** 格式化状态显示 */
      formatStatus(status) {
        const statusMap = {
          '0': '自评',
          '1': '部门领导评分',
          '2': '事业部评分',
          '3': '运改组织部评分',
          '4': '条线领导评分'
        };
        return statusMap[status] || '未知';
      },

      /** 获取状态标签类型 */
      getStatusType(status) {
        const typeMap = {
          '0': 'info',
          '1': 'warning',
          '2': 'primary',
          '3': 'success',
          '4': 'danger'
        };
        return typeMap[status] || 'info';
      },

      /** 取消退回 */
      cancelReturn() {
        this.returnOpen = false;
        this.resetReturnForm();
      },

      /** 重置退回表单 */
      resetReturnForm() {
        this.returnForm = {
          id: null,
          name: null,
          currentStatus: null,
          targetStatus: null
        };
        if (this.$refs.returnForm) {
          this.$refs.returnForm.resetFields();
        }
      },

      /** 确认退回 */
      confirmReturn() {
        const params = {
          id: this.returnForm.id,
          status: this.returnForm.targetStatus
        };

        this.submitReturn(params);
      },

      /** 调用退回接口 */
      submitReturn(params) {
        this.$confirm('确认要退回该评分记录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          rejectInfo(params).then(response => {
            this.$message.success('退回成功');
            this.returnOpen = false;
            this.resetReturnForm();
            this.getList(); // 刷新待评分列表
            this.getCheckedList(); // 刷新已审核列表
          }).catch(error => {
            console.error('退回失败:', error);
            this.$message.error('退回失败，请稍后重试');
          });
        }).catch(() => {
          this.$message.info('已取消退回');
        });
      }
    }
  };
  </script>

  <style scoped>
  .assessment-detail-dialog .detail-container {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: 100vh;
  }

  .assessment-detail-dialog .detail-header h2 {
    background: linear-gradient(135deg, #409EFF, #67C23A);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    font-weight: bold;
  }

  .assessment-detail-dialog .assessment-table-card {
    margin-bottom: 20px;
  }

  .assessment-detail-dialog .signature-card {
    background: #ffffff;
  }

  .signature-content {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }

  .score-text {
    font-weight: 500;
    color: #303133;
  }

  .separator {
    color: #909399;
    margin: 0 4px;
  }

  .signature-name {
    color: #303133;
  }

  .reason-text {
    width: 100%;
    margin-top: 8px;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-left: 3px solid #409EFF;
    border-radius: 4px;
  }

  .reason-label {
    font-weight: 500;
    color: #606266;
    margin-right: 8px;
  }

  .reason-content {
    color: #303133;
    line-height: 1.6;
  }

  .dialog-footer {
    border-top: 1px solid #e4e7ed;
    background-color: #ffffff;
    border-radius: 0 0 6px 6px;
  }

  .assessment-detail-dialog .el-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .assessment-detail-dialog .el-descriptions {
    background-color: #ffffff;
  }

  .assessment-detail-dialog .el-table {
    border-radius: 6px;
    overflow: hidden;
  }

  .text-red {
    color: #F56C6C;
  }
  </style>
