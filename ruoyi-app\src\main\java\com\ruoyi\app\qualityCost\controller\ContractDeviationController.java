package com.ruoyi.app.qualityCost.controller;

import java.math.BigDecimal;
import java.util.List;

import com.ruoyi.app.qualityCost.domain.QualityObjection;
import com.ruoyi.app.qualityCost.domain.RegradeDetail;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.qualityCost.domain.ContractDeviation;
import com.ruoyi.app.qualityCost.service.IContractDeviationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 内部损失成本-产品脱合同损失Controller
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@RestController
@RequestMapping("/qualityCost/contractDeviation")
public class ContractDeviationController extends BaseController
{
    @Autowired
    private IContractDeviationService contractDeviationService;

    /**
     * 查询内部损失成本-产品脱合同损失列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ContractDeviation contractDeviation)
    {
        startPage();
        List<ContractDeviation> list = contractDeviationService.selectContractDeviationList(contractDeviation);
        return getDataTable(list);
    }

    @GetMapping("/listAll")
    public TableDataInfo listAll(ContractDeviation contractDeviation)
    {
        List<ContractDeviation> list = null;
        if (contractDeviation.getSearchMode().equals("模糊搜索")) {
            startPage();
            list = contractDeviationService.selectContractDeviationList(contractDeviation);
        } else if (contractDeviation.getSearchMode().equals("精确搜索")) {
            startPage();
            list = contractDeviationService.selectContractDeviationListByAccurateSearch(contractDeviation);
        }
        return getDataTable(list);
    }

    @GetMapping("/getSum")
    public AjaxResult getSum(ContractDeviation contractDeviation)
    {
        List<ContractDeviation> list = null;
        if (contractDeviation.getSearchMode().equals("模糊搜索")) {
            startPage();
            list = contractDeviationService.selectContractDeviationList(contractDeviation);
        } else if (contractDeviation.getSearchMode().equals("精确搜索")) {
            startPage();
            list = contractDeviationService.selectContractDeviationListByAccurateSearch(contractDeviation);
        }
        BigDecimal sumCostTon = BigDecimal.ZERO;
        BigDecimal sumCostPerTon = BigDecimal.ZERO;
        BigDecimal sumCostEx = BigDecimal.ZERO;
        for (ContractDeviation item : list) {
            if (item.getCostTon() != null) {
                sumCostTon = sumCostTon.add(item.getCostTon());
            }
            if (item.getCostPerTon() != null) {
                sumCostPerTon = sumCostPerTon.add(item.getCostPerTon());
            }
            if (item.getCostEx() != null) {
                sumCostEx = sumCostEx.add(item.getCostEx());
            }
        }
        ContractDeviation sumObj = new ContractDeviation();
        sumObj.setCostTon(sumCostTon);
        sumObj.setCostPerTon(sumCostPerTon);
        sumObj.setCostEx(sumCostEx);

        return AjaxResult.success(sumObj);
    }

    @GetMapping("/getAllSum")
    public AjaxResult getAllSum(ContractDeviation contractDeviation)
    {
        List<ContractDeviation> list = null;
        if (contractDeviation.getSearchMode().equals("模糊搜索")) {
            list = contractDeviationService.selectContractDeviationList(contractDeviation);
        } else if (contractDeviation.getSearchMode().equals("精确搜索")) {
            list = contractDeviationService.selectContractDeviationListByAccurateSearch(contractDeviation);
        }
        BigDecimal sumCostTon = BigDecimal.ZERO;
        BigDecimal sumCostPerTon = BigDecimal.ZERO;
        BigDecimal sumCostEx = BigDecimal.ZERO;
        for (ContractDeviation item : list) {
            if (item.getCostTon() != null) {
                sumCostTon = sumCostTon.add(item.getCostTon());
            }
            if (item.getCostPerTon() != null) {
                sumCostPerTon = sumCostPerTon.add(item.getCostPerTon());
            }
            if (item.getCostEx() != null) {
                sumCostEx = sumCostEx.add(item.getCostEx());
            }
        }
        ContractDeviation sumObj = new ContractDeviation();
        sumObj.setCostTon(sumCostTon);
        sumObj.setCostPerTon(sumCostPerTon);
        sumObj.setCostEx(sumCostEx);

        return AjaxResult.success(sumObj);
    }

    /**
     * 导出内部损失成本-产品脱合同损失列表
     */
    @Log(title = "内部损失成本-产品脱合同损失", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ContractDeviation contractDeviation)
    {
        List<ContractDeviation> list = contractDeviationService.selectContractDeviationList(contractDeviation);
        ExcelUtil<ContractDeviation> util = new ExcelUtil<ContractDeviation>(ContractDeviation.class);
        return util.exportExcel(list, "contractDeviation");
    }

    /**
     * 获取内部损失成本-产品脱合同损失详细信息
     */
    @GetMapping(value = "/{costCenter}")
    public AjaxResult getInfo(@PathVariable("costCenter") String costCenter)
    {
        return AjaxResult.success(contractDeviationService.selectContractDeviationById(costCenter));
    }

    /**
     * 新增内部损失成本-产品脱合同损失
     */
    @Log(title = "内部损失成本-产品脱合同损失", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ContractDeviation contractDeviation)
    {
        return toAjax(contractDeviationService.insertContractDeviation(contractDeviation));
    }

    /**
     * 修改内部损失成本-产品脱合同损失
     */
    @Log(title = "内部损失成本-产品脱合同损失", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContractDeviation contractDeviation)
    {
        return toAjax(contractDeviationService.updateContractDeviation(contractDeviation));
    }

    /**
     * 删除内部损失成本-产品脱合同损失
     */
    @Log(title = "内部损失成本-产品脱合同损失", businessType = BusinessType.DELETE)
	@DeleteMapping("/{costCenters}")
    public AjaxResult remove(@PathVariable String[] costCenters)
    {
        return toAjax(contractDeviationService.deleteContractDeviationByIds(costCenters));
    }
}
